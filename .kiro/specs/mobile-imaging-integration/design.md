# Design Document

## Overview

The mobile imaging integration feature will add new endpoints to both the MenzhenController and ZhuyuanPatientApiController to provide secure access to RadOnline's mobile imaging service. The integration follows a token-based authentication pattern with RSA encryption for patient identifiers, ensuring both security and seamless user experience.

## Architecture

### High-Level Flow

```mermaid
sequenceDiagram
    participant Client as Mobile Client
    participant Controller as Spring Controller
    participant RadOnlineService as RadOnline Service
    participant EncryptionService as RSA Encryption Service
    participant ImageService as RadOnline Image Service

    Client->>Controller: Request imaging URL
    Controller->>Controller: Validate patient session
    Controller->>RadOnlineService: getMobileImagingUrl(patientId)
    
    RadOnlineService->>RadOnlineService: getToken()
    RadOnlineService->>EncryptionService: encryptExamId(patientId)
    EncryptionService-->>RadOnlineService: Return encrypted ID
    RadOnlineService->>RadOnlineService: buildMobileImagingUrl(token, encryptedId)
    
    RadOnlineService-->>Controller: Return complete URL
    Controller-->>Client: Return imaging URL
    Client->>ImageService: Access imaging page
```

### Component Integration

The feature integrates with existing system components:
- **Authentication Layer**: Uses existing `@RequireActivePatient` and `@RequireSession` annotations
- **Patient Management**: Leverages current patient session management
- **Error Handling**: Follows existing `AjaxResult` response patterns
- **Logging**: Uses established logging framework
- **Configuration**: Integrates with system configuration service

## Components and Interfaces

### 1. RadOnline Service

**Purpose**: Unified service for RadOnline token acquisition and URL construction

**Interface**:
```kotlin
@Service
class RadOnlineService {
    fun getToken(): String?
    fun getMobileImagingUrl(patientId: String): String?
}
```

**Implementation**: 
- Direct HTTP call using RestTemplate for token acquisition
- Integrates MobileImagingEncryptionService for patient ID encryption
- URL construction using Spring's UriComponentsBuilder
- Returns null on any failure for simple error handling
- Simple configuration loading from system config
- Minimal error handling with logging

**Key Methods**:
- `getToken()`: Makes HTTP call to RadOnline API, returns token or null
- `getMobileImagingUrl()`: One-stop method - takes raw patient ID, handles token acquisition, encryption, and URL construction

### 2. RSA Encryption Service

**Purpose**: Encrypt patient identifiers for secure transmission using Java standard library

**Implementation**:
- **MobileImagingEncryptionService**: Spring service that handles RSA encryption
- **Direct Java Crypto API usage**: Uses `javax.crypto.Cipher` and `java.security.KeyFactory`
- **Standard library approach**: No custom RSA utility classes needed

**Implementation Pattern**:
- Follow existing Spring service patterns with `@Service` annotation
- Integrate with existing configuration service (`ISysConfigService`)
- Use standard Java cryptography APIs directly
- Follow existing logging patterns using `logger.error()`, `logger.info()` etc.

**Key Features**:
- Uses RSA public key from system configuration
- Direct `Cipher.getInstance("RSA/ECB/PKCS1Padding")` usage
- Base64 encoding via `java.util.Base64`
- Fail-fast error handling with `require()` statements
- Clean API without nullable returns

### 3. Controller Endpoints

#### MenzhenController Extension
```kotlin
@GetMapping("mobileImagingUrl")
@RequireActivePatient(shouldBeMenzhenPatient = true)
fun getMobileImagingUrl(): AjaxResult
```

#### ZhuyuanPatientApiController Extension
```kotlin
@GetMapping("mobileImagingUrl")
@RequireActivePatient(shouldBeZhuyuanPatient = true)
fun getMobileImagingUrl(): AjaxResult
```

**Implementation Pattern**:
- Follow existing endpoint patterns in both controllers
- Use existing annotation patterns (`@RequireActivePatient`, `@RequireSession`)
- Return `AjaxResult` consistent with existing endpoints
- Single method call to RadOnlineService.getMobileImagingUrl(patientId)
- Simple null-check error handling with appropriate AjaxResult responses

## Data Models

### Mobile Imaging Configuration
```kotlin
data class MobileImagingConfig(
    val tokenUrl: String,
    val username: String,
    val password: String,
    val unitId: String,
    val publicKey: String,
    val baseUrl: String = "https://film.radonline.cn/web/fore-end/patient.html"
)
```

## Error Handling

### Error Strategy

Simple null-check based error handling:
- Service methods return null on any failure
- Controllers check for null and return appropriate AjaxResult
- All errors logged with appropriate level

### Error Response Format

All errors follow the existing AjaxResult pattern:
```kotlin
AjaxResult.error("User-friendly error message")
```

### Logging Strategy

- **INFO**: Successful token acquisitions, URL generations
- **WARN**: Retry attempts, configuration issues
- **ERROR**: Authentication failures, encryption errors, system exceptions
- **DEBUG**: Detailed request/response data (non-sensitive only)

## Testing Strategy

### Unit Tests

1. **RadOnline Service Tests**
   - Token acquisition with valid config
   - End-to-end URL generation with getMobileImagingUrl()
   - URL construction with valid parameters (internal method)
   - Null return with missing config
   - Null return on network error
   - Integration with encryption service
   - URL encoding verification

2. **Mobile Imaging Encryption Service Tests**
   - Valid encryption scenarios using standard Java crypto APIs
   - Invalid input handling with proper exception testing
   - Key configuration validation

### Integration Tests

1. **Controller Endpoint Tests**
   - Patient session validation
   - End-to-end URL generation
   - Error response verification
   - Security annotation testing

2. **Service Integration Tests**
   - RadOnline API integration (with test credentials)
   - Configuration loading
   - Cross-component interaction

### Security Tests

1. **Encryption Validation**
   - RSA encryption correctness
   - Data integrity verification
   - Key management security

2. **Authentication Tests**
   - Token security validation
   - Session management verification
   - Access control testing

## Configuration Management

### System Configuration Keys

- `mobile-imaging.enabled`: Feature toggle (default: true)
- `mobile-imaging.token-url`: RadOnline token endpoint
- `mobile-imaging.username`: API username
- `mobile-imaging.password`: API password (encrypted in config)
- `mobile-imaging.unit-id`: Hospital identifier (required)
- `mobile-imaging.base-url`: RadOnline imaging service URL (required)
- `mobile-imaging.public-key`: RSA public key for encryption

### Environment-Specific Settings

- **Development**: Test credentials and endpoints
- **Staging**: Staging environment configuration
- **Production**: Production credentials with enhanced security

## Performance Considerations

### Caching Strategy

- **Token Caching**: Cache valid tokens with appropriate TTL
- **Configuration Caching**: Cache system configuration to reduce database calls

### Scalability

- **Async Processing**: Use coroutines for API calls to prevent blocking
- **Connection Pooling**: Configure HTTP client with appropriate connection limits
- **Rate Limiting**: Implement client-side rate limiting for RadOnline API calls

### Monitoring

- **Metrics**: Track token acquisition success rates, response times
- **Alerts**: Configure alerts for authentication failures, service unavailability
- **Health Checks**: Implement health check endpoints for monitoring

## Security Considerations

### Data Protection

- **Encryption**: All patient identifiers encrypted before transmission
- **Key Management**: Secure storage and rotation of RSA keys
- **Audit Logging**: Log all access attempts and security events

### Access Control

- **Session Validation**: Enforce existing patient session requirements
- **Patient Verification**: Validate patient identity before URL generation
- **Service Authorization**: Ensure proper authorization for imaging access

### Network Security

- **HTTPS Only**: All external API calls use HTTPS
- **Certificate Validation**: Validate SSL certificates for external services
- **Timeout Configuration**: Prevent hanging connections and resource exhaustion