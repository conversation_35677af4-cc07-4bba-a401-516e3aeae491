# Requirements Document

## Introduction

This feature integrates the third-party mobile imaging service (移动影像) into the existing healthcare system. The integration will provide both outpatient (门诊) and inpatient (住院) patients with access to their medical imaging reports through a secure token-based authentication system. The system will handle RSA encryption for patient identification and provide seamless access to imaging data through the RadOnline platform.

## Requirements

### Requirement 1

**User Story:** As an outpatient (门诊患者), I want to access my medical imaging reports through the mobile imaging service, so that I can view my examination results conveniently.

#### Acceptance Criteria

1. WHEN an outpatient requests access to mobile imaging THEN the system SHALL generate a secure token using the RadOnline API
2. WHEN the token is successfully obtained THEN the system SHALL encrypt the patient's ID card number using RSA encryption
3. WHEN the encrypted patient ID is ready THEN the system SHALL construct the complete URL with required parameters (token, unitid, xeguid, hasList, callBack)
4. WHEN the URL is constructed THEN the system SHALL return the complete imaging service URL to the patient
5. IF the token generation fails THEN the system SHALL return an appropriate error message
6. IF the patient's ID card number is invalid THEN the system SHALL return a validation error

### Requirement 2

**User Story:** As an inpatient (住院患者), I want to access my medical imaging reports through the mobile imaging service, so that I can view my examination results during my hospital stay.

#### Acceptance Criteria

1. WHEN an inpatient requests access to mobile imaging THEN the system SHALL generate a secure token using the RadOnline API
2. WHEN the token is successfully obtained THEN the system SHALL encrypt the patient's admission number or ID card number using RSA encryption
3. WHEN the encrypted patient identifier is ready THEN the system SHALL construct the complete URL with required parameters specific to inpatient context
4. WHEN the URL is constructed THEN the system SHALL return the complete imaging service URL to the patient
5. IF the patient is not currently admitted THEN the system SHALL return an error indicating invalid patient status
6. IF the admission number is invalid THEN the system SHALL return a validation error

### Requirement 3

**User Story:** As a system administrator, I want the mobile imaging integration to use secure RSA encryption for patient identifiers, so that patient privacy and data security are maintained.

#### Acceptance Criteria

1. WHEN patient identifiers need to be transmitted THEN the system SHALL encrypt them using RSA public key encryption
2. WHEN RSA encryption is performed THEN the system SHALL use the configured public key from system settings
3. WHEN encryption succeeds THEN the system SHALL return a Base64-encoded encrypted string
4. IF RSA encryption fails THEN the system SHALL log the error and return a generic error message
5. WHEN handling encryption errors THEN the system SHALL NOT expose sensitive information in error messages

### Requirement 4

**User Story:** As a system administrator, I want the mobile imaging integration to handle token management efficiently, so that the system performs well under load.

#### Acceptance Criteria

1. WHEN requesting tokens from RadOnline API THEN the system SHALL use proper HTTP client configuration with timeouts
2. WHEN API calls fail THEN the system SHALL implement appropriate retry logic with exponential backoff
3. WHEN tokens are obtained THEN the system SHALL validate the response format before proceeding
4. IF the RadOnline API is unavailable THEN the system SHALL return a user-friendly error message
5. WHEN multiple concurrent requests occur THEN the system SHALL handle them efficiently without blocking

### Requirement 5

**User Story:** As a healthcare provider, I want the mobile imaging URLs to include proper configuration parameters, so that patients have the best user experience when viewing their reports.

#### Acceptance Criteria

1. WHEN constructing imaging URLs THEN the system SHALL include the hasList parameter set to true for multiple results support
2. WHEN constructing imaging URLs THEN the system SHALL include the callBack parameter set to true for navigation support
3. WHEN constructing imaging URLs THEN the system SHALL use the correct unitid parameter for hospital identification
4. WHEN constructing imaging URLs THEN the system SHALL append the correct route fragment (#/check-detail) for direct access
5. IF any required URL parameters are missing THEN the system SHALL return a configuration error

### Requirement 6

**User Story:** As a developer, I want the mobile imaging integration to follow the existing codebase patterns and error handling, so that the code is maintainable and consistent.

#### Acceptance Criteria

1. WHEN implementing new endpoints THEN the system SHALL use the existing annotation patterns (@RequireActivePatient, @RequireSession)
2. WHEN returning responses THEN the system SHALL use the standard AjaxResult format
3. WHEN handling errors THEN the system SHALL follow the existing error handling patterns
4. WHEN logging events THEN the system SHALL use the existing logging framework and patterns
5. WHEN validating inputs THEN the system SHALL use existing validation utilities where applicable