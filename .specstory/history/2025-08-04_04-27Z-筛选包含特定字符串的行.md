<!-- Generated by SpecStory -->

# 筛选包含特定字符串的行 (2025-08-04 04:27Z)

_**User**_

筛选出所有包含`/open/dict/minzu`的行

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要筛选出日志文件中包含`/open/dict/minzu`的行。根据上下文信息，用户正在查看一个访问日志文件 `access_log.2025-08-04.log`，文件很大有75870行。

我需要使用grep_search工具来搜索包含`/open/dict/minzu`的行。这是一个精确的文本搜索任务，适合使用grep_search而不是codebase_search。

我需要转义斜杠字符，所以模式应该是 `/open/dict/minzu`，但实际上在这种情况下，斜杠不需要转义，因为它们不是特殊的正则表达式字符。</details></think>

---

我来帮您筛选出日志文件中包含`/open/dict/minzu`的所有行。

---

<details>
            <summary>Grep search for "/open/dict/minzu" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L376 | `************** [04/Aug/2025:00:03:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2398` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L540 | `39.144.196.28 [04/Aug/2025:00:04:49 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1861` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L650 | `39.144.30.12 [04/Aug/2025:00:05:43 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2129` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L668 | `36.183.17.124 [04/Aug/2025:00:06:04 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2284` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L833 | `221.7.7.32 [04/Aug/2025:00:07:37 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1658` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L940 | `116.178.120.31 [04/Aug/2025:00:08:40 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1271` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L988 | `112.43.77.145 [04/Aug/2025:00:08:55 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1493` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1395 | `39.144.196.38 [04/Aug/2025:00:14:34 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2897` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1440 | `223.114.17.68 [04/Aug/2025:00:15:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1851` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1448 | `116.178.123.208 [04/Aug/2025:00:15:42 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1688` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1603 | `39.144.33.145 [04/Aug/2025:00:18:23 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1769` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1719 | `49.118.85.47 [04/Aug/2025:00:19:34 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1729` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1789 | `************* [04/Aug/2025:00:21:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1901` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1831 | `124.88.64.14 [04/Aug/2025:00:22:25 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1314` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1925 | `223.160.182.144 [04/Aug/2025:00:25:04 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1810` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1963 | `124.88.1.140 [04/Aug/2025:00:27:46 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1301` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2045 | `124.88.1.161 [04/Aug/2025:00:31:36 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1854` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2267 | `************* [04/Aug/2025:00:37:46 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1795` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2270 | `43.242.152.61 [04/Aug/2025:00:37:46 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1435` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2339 | `39.144.197.0 [04/Aug/2025:00:39:12 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1403` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2469 | `************* [04/Aug/2025:00:41:28 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1307` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2542 | `36.184.163.155 [04/Aug/2025:00:42:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1766` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2717 | `110.154.70.219 [04/Aug/2025:00:45:01 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1380` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2972 | `223.116.80.225 [04/Aug/2025:00:50:00 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1706` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3172 | `************6 [04/Aug/2025:00:54:07 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1791` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3216 | `39.144.33.240 [04/Aug/2025:00:54:44 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1350` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3355 | `124.117.85.93 [04/Aug/2025:00:56:29 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1448` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3514 | `************** [04/Aug/2025:00:59:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1457` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3657 | `124.88.1.220 [04/Aug/2025:01:03:01 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1807` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3734 | `110.152.188.0 [04/Aug/2025:01:04:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2901` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L4196 | `110.154.17.198 [04/Aug/2025:01:14:32 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1665` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5256 | `112.43.80.66 [04/Aug/2025:01:29:22 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1852` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5814 | `112.43.24.126 [04/Aug/2025:01:38:05 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1255` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5892 | `112.43.24.83 [04/Aug/2025:01:38:45 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1909` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L6285 | `110.152.38.187 [04/Aug/2025:01:44:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2178` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7018 | `110.154.17.198 [04/Aug/2025:01:54:13 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2997` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7565 | `106.123.144.201 [04/Aug/2025:02:15:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1903` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7660 | `39.144.196.207 [04/Aug/2025:02:26:24 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 3112` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7774 | `39.144.197.51 [04/Aug/2025:02:32:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2825` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8057 | `************ [04/Aug/2025:02:37:52 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1546` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8425 | `116.178.222.165 [04/Aug/2025:03:14:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1912` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8500 | `110.157.88.145 [04/Aug/2025:03:24:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1813` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8614 | `43.242.155.223 [04/Aug/2025:03:37:47 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1962` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8831 | `110.154.68.199 [04/Aug/2025:05:01:58 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1978` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8900 | `49.115.226.110 [04/Aug/2025:05:04:21 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2003` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L9646 | `112.43.117.75 [04/Aug/2025:07:12:16 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1989` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L9705 | `223.104.40.237 [04/Aug/2025:07:13:53 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1450` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10200 | `39.144.197.29 [04/Aug/2025:07:40:22 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1966` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10258 | `39.144.69.121 [04/Aug/2025:07:43:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1896` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10460 | `106.123.147.129 [04/Aug/2025:07:53:18 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1889` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10499 | `106.123.150.115 [04/Aug/2025:08:00:45 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1912` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10601 | `112.43.77.238 [04/Aug/2025:08:04:53 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1886` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10753 | `110.156.236.1 [04/Aug/2025:08:13:06 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2517` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10915 | `112.43.32.67 [04/Aug/2025:08:22:07 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2061` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11122 | `116.178.215.174 [04/Aug/2025:08:29:08 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1909` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11148 | `************* [04/Aug/2025:08:29:40 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1840` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11231 | `39.144.196.22 [04/Aug/2025:08:31:02 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1966` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11556 | `221.7.8.190 [04/Aug/2025:08:37:54 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1869` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11901 | `39.144.197.30 [04/Aug/2025:08:43:47 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1897` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11922 | `49.112.224.139 [04/Aug/2025:08:44:16 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1645` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11951 | `39.144.32.68 [04/Aug/2025:08:44:54 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1804` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12199 | `39.144.196.52 [04/Aug/2025:08:48:17 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1861` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12222 | `110.154.69.7 [04/Aug/2025:08:48:33 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1451` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12431 | `49.112.225.90 [04/Aug/2025:08:51:03 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1907` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12457 | `36.108.9.49 [04/Aug/2025:08:51:13 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2185` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12597 | `39.144.197.13 [04/Aug/2025:08:53:18 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2518` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12717 | `39.144.196.40 [04/Aug/2025:08:54:23 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1408` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12763 | `39.144.197.15 [04/Aug/2025:08:54:43 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2978` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12961 | `43.242.154.234 [04/Aug/2025:08:56:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1617` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13421 | `116.178.193.31 [04/Aug/2025:09:01:24 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1870` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13649 | `101.230.184.211 [04/Aug/2025:09:03:57 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2317` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13653 | `39.144.196.64 [04/Aug/2025:09:04:00 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2635` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13809 | `************1 [04/Aug/2025:09:05:39 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1905` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13917 | `110.152.66.200 [04/Aug/2025:09:06:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1558` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13919 | `110.152.181.244 [04/Aug/2025:09:06:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1441` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14003 | `112.43.8.92 [04/Aug/2025:09:06:56 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1707` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14090 | `39.144.196.15 [04/Aug/2025:09:07:35 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2252` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14372 | `************ [04/Aug/2025:09:09:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1513` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14474 | `39.144.196.52 [04/Aug/2025:09:10:06 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1224` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14622 | `221.7.8.195 [04/Aug/2025:09:11:21 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2622` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14663 | `39.144.32.8 [04/Aug/2025:09:11:49 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1870` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14825 | `************** [04/Aug/2025:09:13:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1441` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14907 | `39.144.196.52 [04/Aug/2025:09:13:49 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1400` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15260 | `43.242.152.9 [04/Aug/2025:09:17:06 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1885` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15645 | `39.144.209.144 [04/Aug/2025:09:20:10 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1155` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15980 | `112.43.126.238 [04/Aug/2025:09:22:53 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1764` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16223 | `39.144.196.38 [04/Aug/2025:09:24:24 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1749` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16393 | `39.144.33.223 [04/Aug/2025:09:25:22 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1520` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16883 | `39.144.196.108 [04/Aug/2025:09:29:40 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1717` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16939 | `39.144.208.206 [04/Aug/2025:09:30:16 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1337` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17124 | `39.144.196.28 [04/Aug/2025:09:31:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1535` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17263 | `39.144.196.111 [04/Aug/2025:09:32:08 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1523` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17273 | `39.144.33.112 [04/Aug/2025:09:32:10 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2485` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17470 | `116.178.193.239 [04/Aug/2025:09:32:55 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2163` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17565 | `112.43.81.121 [04/Aug/2025:09:33:26 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2221` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17725 | `39.144.33.230 [04/Aug/2025:09:34:11 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2214` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17770 | `39.144.197.43 [04/Aug/2025:09:34:21 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1614` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17796 | `110.157.85.179 [04/Aug/2025:09:34:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1349` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17797 | `************* [04/Aug/2025:09:34:29 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1830` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17939 | `************** [04/Aug/2025:09:34:57 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1260` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L376 | `************** [04/Aug/2025:00:03:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2398` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L540 | `39.144.196.28 [04/Aug/2025:00:04:49 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1861` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L650 | `39.144.30.12 [04/Aug/2025:00:05:43 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2129` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L668 | `36.183.17.124 [04/Aug/2025:00:06:04 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2284` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L833 | `221.7.7.32 [04/Aug/2025:00:07:37 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1658` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L940 | `116.178.120.31 [04/Aug/2025:00:08:40 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1271` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L988 | `112.43.77.145 [04/Aug/2025:00:08:55 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1493` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1395 | `39.144.196.38 [04/Aug/2025:00:14:34 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2897` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1440 | `223.114.17.68 [04/Aug/2025:00:15:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1851` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1448 | `116.178.123.208 [04/Aug/2025:00:15:42 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1688` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1603 | `39.144.33.145 [04/Aug/2025:00:18:23 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1769` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1719 | `49.118.85.47 [04/Aug/2025:00:19:34 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1729` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1789 | `************* [04/Aug/2025:00:21:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1901` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1831 | `124.88.64.14 [04/Aug/2025:00:22:25 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1314` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1925 | `223.160.182.144 [04/Aug/2025:00:25:04 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1810` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1963 | `124.88.1.140 [04/Aug/2025:00:27:46 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1301` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2045 | `124.88.1.161 [04/Aug/2025:00:31:36 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1854` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2267 | `************* [04/Aug/2025:00:37:46 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1795` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2270 | `43.242.152.61 [04/Aug/2025:00:37:46 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1435` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2339 | `39.144.197.0 [04/Aug/2025:00:39:12 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1403` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2469 | `************* [04/Aug/2025:00:41:28 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1307` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2542 | `36.184.163.155 [04/Aug/2025:00:42:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1766` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2717 | `110.154.70.219 [04/Aug/2025:00:45:01 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1380` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2972 | `223.116.80.225 [04/Aug/2025:00:50:00 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1706` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3172 | `************6 [04/Aug/2025:00:54:07 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1791` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3216 | `39.144.33.240 [04/Aug/2025:00:54:44 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1350` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3355 | `124.117.85.93 [04/Aug/2025:00:56:29 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1448` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3514 | `************** [04/Aug/2025:00:59:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1457` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3657 | `124.88.1.220 [04/Aug/2025:01:03:01 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1807` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3734 | `110.152.188.0 [04/Aug/2025:01:04:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2901` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L4196 | `110.154.17.198 [04/Aug/2025:01:14:32 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1665` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5256 | `112.43.80.66 [04/Aug/2025:01:29:22 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1852` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5814 | `112.43.24.126 [04/Aug/2025:01:38:05 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1255` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5892 | `112.43.24.83 [04/Aug/2025:01:38:45 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1909` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L6285 | `110.152.38.187 [04/Aug/2025:01:44:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2178` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7018 | `110.154.17.198 [04/Aug/2025:01:54:13 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2997` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7565 | `106.123.144.201 [04/Aug/2025:02:15:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1903` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7660 | `39.144.196.207 [04/Aug/2025:02:26:24 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 3112` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7774 | `39.144.197.51 [04/Aug/2025:02:32:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2825` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8057 | `************ [04/Aug/2025:02:37:52 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1546` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8425 | `116.178.222.165 [04/Aug/2025:03:14:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1912` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8500 | `110.157.88.145 [04/Aug/2025:03:24:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1813` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8614 | `43.242.155.223 [04/Aug/2025:03:37:47 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1962` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8831 | `110.154.68.199 [04/Aug/2025:05:01:58 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1978` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8900 | `49.115.226.110 [04/Aug/2025:05:04:21 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2003` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L9646 | `112.43.117.75 [04/Aug/2025:07:12:16 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1989` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L9705 | `223.104.40.237 [04/Aug/2025:07:13:53 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1450` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10200 | `39.144.197.29 [04/Aug/2025:07:40:22 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1966` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10258 | `39.144.69.121 [04/Aug/2025:07:43:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1896` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10460 | `106.123.147.129 [04/Aug/2025:07:53:18 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1889` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10499 | `106.123.150.115 [04/Aug/2025:08:00:45 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1912` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10601 | `112.43.77.238 [04/Aug/2025:08:04:53 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1886` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10753 | `110.156.236.1 [04/Aug/2025:08:13:06 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2517` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10915 | `112.43.32.67 [04/Aug/2025:08:22:07 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2061` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11122 | `116.178.215.174 [04/Aug/2025:08:29:08 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1909` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11148 | `************* [04/Aug/2025:08:29:40 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1840` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11231 | `39.144.196.22 [04/Aug/2025:08:31:02 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1966` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11556 | `221.7.8.190 [04/Aug/2025:08:37:54 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1869` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11901 | `39.144.197.30 [04/Aug/2025:08:43:47 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1897` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11922 | `49.112.224.139 [04/Aug/2025:08:44:16 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1645` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11951 | `39.144.32.68 [04/Aug/2025:08:44:54 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1804` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12199 | `39.144.196.52 [04/Aug/2025:08:48:17 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1861` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12222 | `110.154.69.7 [04/Aug/2025:08:48:33 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1451` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12431 | `49.112.225.90 [04/Aug/2025:08:51:03 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1907` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12457 | `36.108.9.49 [04/Aug/2025:08:51:13 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2185` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12597 | `39.144.197.13 [04/Aug/2025:08:53:18 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2518` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12717 | `39.144.196.40 [04/Aug/2025:08:54:23 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1408` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12763 | `39.144.197.15 [04/Aug/2025:08:54:43 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2978` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12961 | `43.242.154.234 [04/Aug/2025:08:56:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1617` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13421 | `116.178.193.31 [04/Aug/2025:09:01:24 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1870` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13649 | `101.230.184.211 [04/Aug/2025:09:03:57 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2317` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13653 | `39.144.196.64 [04/Aug/2025:09:04:00 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2635` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13809 | `************1 [04/Aug/2025:09:05:39 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1905` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13917 | `110.152.66.200 [04/Aug/2025:09:06:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1558` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13919 | `110.152.181.244 [04/Aug/2025:09:06:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1441` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14003 | `112.43.8.92 [04/Aug/2025:09:06:56 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1707` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14090 | `39.144.196.15 [04/Aug/2025:09:07:35 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2252` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14372 | `************ [04/Aug/2025:09:09:14 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1513` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14474 | `39.144.196.52 [04/Aug/2025:09:10:06 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1224` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14622 | `221.7.8.195 [04/Aug/2025:09:11:21 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2622` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14663 | `39.144.32.8 [04/Aug/2025:09:11:49 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1870` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14825 | `************** [04/Aug/2025:09:13:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1441` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14907 | `39.144.196.52 [04/Aug/2025:09:13:49 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1400` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15260 | `43.242.152.9 [04/Aug/2025:09:17:06 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1885` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15645 | `39.144.209.144 [04/Aug/2025:09:20:10 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1155` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15980 | `112.43.126.238 [04/Aug/2025:09:22:53 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1764` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16223 | `39.144.196.38 [04/Aug/2025:09:24:24 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1749` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16393 | `39.144.33.223 [04/Aug/2025:09:25:22 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1520` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16883 | `39.144.196.108 [04/Aug/2025:09:29:40 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1717` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16939 | `39.144.208.206 [04/Aug/2025:09:30:16 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1337` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17124 | `39.144.196.28 [04/Aug/2025:09:31:30 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1535` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17263 | `39.144.196.111 [04/Aug/2025:09:32:08 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1523` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17273 | `39.144.33.112 [04/Aug/2025:09:32:10 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2485` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17470 | `116.178.193.239 [04/Aug/2025:09:32:55 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2163` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17565 | `112.43.81.121 [04/Aug/2025:09:33:26 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2221` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17725 | `39.144.33.230 [04/Aug/2025:09:34:11 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2214` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17770 | `39.144.197.43 [04/Aug/2025:09:34:21 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1614` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17796 | `110.157.85.179 [04/Aug/2025:09:34:27 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1349` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17797 | `************* [04/Aug/2025:09:34:29 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1830` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17939 | `************** [04/Aug/2025:09:34:57 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 1260` |

</details>

---

<think><details><summary>Thought Process</summary>搜索结果显示了很多包含`/open/dict/minzu`的访问日志行。结果显示了日志文件中出现这个路径的所有位置，每一行都包含了IP地址、时间戳、HTTP请求方法、状态码、响应大小和处理时间等信息。

从结果可以看出：
1. 搜索找到了很多结果，但由于结果太多，工具只显示了前50个匹配项
2. 这些都是对`/open/dict/minzu`这个API端点的GET请求
3. 所有请求都返回了200状态码，响应大小为15860字节
4. 请求时间主要集中在2025年8月4日的早上时段
5. 响应时间在1155到3112毫秒之间变化

这似乎是一个民族字典相关的API接口，被频繁访问。</details></think>

---

已成功筛选出包含`/open/dict/minzu`的日志行。从搜索结果可以看出：

## 📊 筛选结果概览

找到了大量包含`/open/dict/minzu`的访问记录（显示了前50条，实际还有更多）。

## 🔍 关键信息分析

从筛选出的日志行可以看到以下模式：

- **请求路径**: `/open/dict/minzu` (民族字典API接口)
- **请求方法**: 全部为 `GET` 请求
- **HTTP状态码**: 全部为 `200` (成功)
- **响应大小**: 固定为 `15860` 字节
- **访问时间**: 主要集中在 2025年8月4日 00:03 - 09:34 时间段
- **响应时间**: 1155ms - 3112ms 之间（最后一列数字）

## 📈 访问特征

1. **高频访问**: 在约9小时内有大量访问记录
2. **稳定响应**: 所有请求都成功返回相同大小的数据
3. **分布式来源**: 来自多个不同IP地址的访问
4. **性能稳定**: 响应时间相对稳定，大多数在1-3秒之间

如果您需要进一步分析特定时间段的访问或统计更详细的信息，请告诉我！

---

_**User**_

筛选出所有响应时间大于10秒的行，保存到 @slow.log 

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要从访问日志中筛选出响应时间大于10秒（10000毫秒）的行，并保存到slow.log文件中。

从之前的日志分析可以看到，日志格式是：
IP地址 [时间戳] "请求方法 路径 HTTP版本" 状态码 响应大小 响应时间(毫秒)

例如：
************** [04/Aug/2025:00:03:19 +0800] "GET /open/dict/minzu HTTP/1.1" 200 15860 2398

最后一个数字是响应时间，单位是毫秒。我需要找出响应时间大于10000毫秒（10秒）的行。

我可以使用grep搜索来找到这些行，然后将结果保存到slow.log文件中。

让我先搜索响应时间大于10000的行。由于日志格式是以空格分隔，响应时间是最后一个字段，我需要用正则表达式来匹配。

我需要匹配最后一个数字大于10000的行。可以用正则表达式匹配 1[0-9]{4,} 或者 [2-9][0-9]{4,} 来匹配大于等于10000的数字在行末。</details></think>

---

我来帮您筛选出响应时间大于10秒（10000毫秒）的日志行，并保存到`slow.log`文件中。

---

<details>
            <summary>Grep search for " (1[0-9]{4,}|[2-9][0-9]{4,})$" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1 | `************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" ************97` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 345478` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3 | `************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 389992` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L4 | `************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/tickets?doctorCode=574&date=2025-08-11&ampm=1 HTTP/1.1" 200 398 22551` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5 | `************* [04/Aug/2025:00:00:03 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 201562` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L6 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 238800` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7 | `************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 385224` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 234764` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10 | `************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 263585` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11 | `************* [04/Aug/2025:00:00:06 +0800] "POST /api/reservation HTTP/1.1" 200 107 131018` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12 | `************* [04/Aug/2025:00:00:08 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 316389` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13 | `************* [04/Aug/2025:00:00:09 +0800] "POST /api/reservation HTTP/1.1" 200 107 130837` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14 | `************ [04/Aug/2025:00:00:10 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 47 149079` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15 | `************* [04/Aug/2025:00:00:12 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-09 HTTP/1.1" 200 700 284261` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16 | `************* [04/Aug/2025:00:00:12 +0800] "GET /api/reservation/history HTTP/1.1" 200 3354 349203` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17 | `************* [04/Aug/2025:00:00:13 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-08 HTTP/1.1" 200 2205 310827` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L18 | `************* [04/Aug/2025:00:00:14 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-07 HTTP/1.1" 200 3748 323416` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L19 | `************* [04/Aug/2025:00:00:14 +0800] "GET /api/patient/active HTTP/1.1" 200 803 134023` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L20 | `************* [04/Aug/2025:00:00:15 +0800] "GET /api/erhcCard/getQrCode?patientId=987858 HTTP/1.1" 200 422 162035` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L21 | `************* [04/Aug/2025:00:00:15 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 333279` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L22 | `************* [04/Aug/2025:00:00:15 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 213377` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L24 | `************* [04/Aug/2025:00:00:19 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-09 HTTP/1.1" 200 700 322264` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L26 | `************* [04/Aug/2025:00:00:23 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 315672` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L27 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 241318` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L28 | `************* [04/Aug/2025:00:00:23 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 331671` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L29 | `************* [04/Aug/2025:00:00:24 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 290581` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L30 | `********** [04/Aug/2025:00:00:24 +0800] "GET /open/config HTTP/1.0" 200 723 63972` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L31 | `************ [04/Aug/2025:00:00:25 +0800] "POST /open/wx/login HTTP/1.0" 200 95 442143` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L32 | `************* [04/Aug/2025:00:00:26 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 310297` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L34 | `************* [04/Aug/2025:00:00:27 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 291001` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L35 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 317447` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L36 | `************* [04/Aug/2025:00:00:27 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-07 HTTP/1.1" 200 3748 288849` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L37 | `************* [04/Aug/2025:00:00:29 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 278681` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L38 | `************* [04/Aug/2025:00:00:29 +0800] "GET /api/patient/active HTTP/1.0" 200 62 126568` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L39 | `************* [04/Aug/2025:00:00:29 +0800] "GET /open/config HTTP/1.1" 200 735 62955` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L40 | `************* [04/Aug/2025:00:00:29 +0800] "GET /api/patient/active HTTP/1.1" 200 735 126805` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L42 | `************* [04/Aug/2025:00:00:30 +0800] "GET /api/patient/menzhenBalance?patientId=1240879&id=1828 HTTP/1.1" 200 54 206644` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L43 | `************* [04/Aug/2025:00:00:31 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 310693` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L44 | `************ [04/Aug/2025:00:00:32 +0800] "GET /api/patient/active HTTP/1.0" 200 62 126492` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L45 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 277563` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L46 | `************* [04/Aug/2025:00:00:33 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 290249` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L47 | `************* [04/Aug/2025:00:00:34 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 291613` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L48 | `************* [04/Aug/2025:00:00:35 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 313718` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L49 | `************* [04/Aug/2025:00:00:35 +0800] "GET /api/patient/menzhenBalance?patientId=1240879&id=1828 HTTP/1.1" 200 54 200618` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L50 | `************* [04/Aug/2025:00:00:36 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 324632` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L51 | `************* [04/Aug/2025:00:00:36 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 282430` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L52 | `************* [04/Aug/2025:00:00:37 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-07 HTTP/1.1" 200 3748 278566` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L53 | `************* [04/Aug/2025:00:00:41 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 286824` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L54 | `************* [04/Aug/2025:00:00:42 +0800] "GET /api/patient/active HTTP/1.1" 200 735 132336` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L55 | `************* [04/Aug/2025:00:00:42 +0800] "GET /api/patient/menzhenBalance?patientId=1240879&id=1828 HTTP/1.1" 200 54 205511` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L58 | `************* [04/Aug/2025:00:00:44 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-09 HTTP/1.1" 200 700 305801` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L59 | `************* [04/Aug/2025:00:00:46 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-08 HTTP/1.1" 200 2205 316888` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L60 | `************* [04/Aug/2025:00:00:46 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 289476` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L61 | `************* [04/Aug/2025:00:00:46 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 322831` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L62 | `************* [04/Aug/2025:00:00:48 +0800] "GET /api/patient/active HTTP/1.1" 200 803 128846` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L63 | `************* [04/Aug/2025:00:00:48 +0800] "GET /api/erhcCard/getQrCode?patientId=987858 HTTP/1.1" 200 422 155258` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L65 | `************* [04/Aug/2025:00:00:48 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 210524` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L67 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 293407` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L68 | `************* [04/Aug/2025:00:00:52 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 318506` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L69 | `************ [04/Aug/2025:00:00:52 +0800] "GET /api/patient/active HTTP/1.1" 200 821 133783` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L70 | `************ [04/Aug/2025:00:00:53 +0800] "GET /api/erhcCard/getQrCode?patientId=1214546 HTTP/1.1" 200 469 160590` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L71 | `************* [04/Aug/2025:00:00:53 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 224154` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L72 | `************* [04/Aug/2025:00:00:53 +0800] "GET /api/reservation/history HTTP/1.1" 200 3354 360548` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L73 | `************* [04/Aug/2025:00:00:54 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-04 HTTP/1.1" 200 1753 608970` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L74 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 252006` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L75 | `************* [04/Aug/2025:00:00:55 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 304820` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L76 | `************ [04/Aug/2025:00:00:56 +0800] "GET /api/patient/active HTTP/1.1" 200 62 134716` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L77 | `************ [04/Aug/2025:00:00:56 +0800] "GET /api/patient/active HTTP/1.1" 200 62 135749` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L78 | `************ [04/Aug/2025:00:00:56 +0800] "GET /api/patient/active HTTP/1.1" 200 62 134738` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L79 | `************ [04/Aug/2025:00:00:57 +0800] "GET /api/patient/active HTTP/1.1" 200 62 134776` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L80 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 254803` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L81 | `************* [04/Aug/2025:00:00:57 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 284019` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L83 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 255129` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L84 | `************* [04/Aug/2025:00:01:01 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 294991` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L85 | `************* [04/Aug/2025:00:01:03 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-05 HTTP/1.1" 200 1443 323316` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L86 | `************* [04/Aug/2025:00:01:05 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-04 HTTP/1.1" 200 1753 332586` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L87 | `************* [04/Aug/2025:00:01:06 +0800] "GET /api/patient/active HTTP/1.1" 200 803 147046` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L88 | `************* [04/Aug/2025:00:01:06 +0800] "GET /api/erhcCard/getQrCode?patientId=987858 HTTP/1.1" 200 422 163242` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L89 | `************* [04/Aug/2025:00:01:06 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 208578` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L90 | `************* [04/Aug/2025:00:01:06 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-04 HTTP/1.1" 200 1753 317397` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L91 | `36.184.164.163 [04/Aug/2025:00:01:07 +0800] "GET /open/schedule/tickets?doctorCode=577&date=2025-08-04&ampm=2 HTTP/1.1" 200 398 19508` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L92 | `36.184.164.163 [04/Aug/2025:00:01:07 +0800] "GET /api/patient/menzhenBalance?patientId=924159&id=3341 HTTP/1.1" 200 54 204943` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L93 | `49.118.18.30 [04/Aug/2025:00:01:08 +0800] "GET /open/config HTTP/1.1" 200 735 64186` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L94 | `49.118.18.30 [04/Aug/2025:00:01:08 +0800] "GET /api/patient/active HTTP/1.1" 200 677 129139` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L96 | `49.118.18.30 [04/Aug/2025:00:01:08 +0800] "GET /api/patient/menzhenBalance?patientId=835976&id=2577 HTTP/1.1" 200 55 212765` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L97 | `49.118.18.30 [04/Aug/2025:00:01:09 +0800] "POST /open/wx/login HTTP/1.1" 200 106 433492` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L99 | `49.118.18.30 [04/Aug/2025:00:01:09 +0800] "GET /api/patient/active HTTP/1.1" 200 677 133273` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L100 | `36.184.164.163 [04/Aug/2025:00:01:10 +0800] "POST /api/reservation HTTP/1.1" 200 359 549611` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L101 | `36.184.164.163 [04/Aug/2025:00:01:11 +0800] "GET /api/patient/menzhenBalance?patientId=924159&id=3341 HTTP/1.1" 200 54 214778` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L102 | `36.184.164.163 [04/Aug/2025:00:01:11 +0800] "GET /api/reservation/history HTTP/1.1" 200 9751 348492` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L103 | `36.184.106.237 [04/Aug/2025:00:01:11 +0800] "GET /api/patient/active HTTP/1.1" 200 803 145536` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L104 | `************* [04/Aug/2025:00:01:11 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-05 HTTP/1.1" 200 1443 306572` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L105 | `36.184.106.237 [04/Aug/2025:00:01:12 +0800] "GET /api/erhcCard/getQrCode?patientId=832549 HTTP/1.1" 200 422 156739` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L106 | `************* [04/Aug/2025:00:01:12 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-06 HTTP/1.1" 200 1599 327539` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L107 | `36.184.106.237 [04/Aug/2025:00:01:12 +0800] "GET /api/patient/menzhenBalance?patientId=832549&id=1634 HTTP/1.1" 200 56 214867` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L108 | `175.24.211.233 [04/Aug/2025:00:01:12 +0800] "GET /open/config HTTP/1.0" 200 723 66667` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L111 | `************* [04/Aug/2025:00:01:13 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-07 HTTP/1.1" 200 1443 306938` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L112 | `************* [04/Aug/2025:00:01:14 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-08 HTTP/1.1" 200 979 290994` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L113 | `************* [04/Aug/2025:00:01:14 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 294525` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L114 | `************* [04/Aug/2025:00:01:14 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-09 HTTP/1.1" 200 979 337357` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L1 | `************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" ************97` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L2 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 345478` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L3 | `************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 389992` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L4 | `************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/tickets?doctorCode=574&date=2025-08-11&ampm=1 HTTP/1.1" 200 398 22551` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L5 | `************* [04/Aug/2025:00:00:03 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 201562` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L6 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 238800` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L7 | `************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 385224` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L8 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 234764` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L10 | `************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 263585` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L11 | `************* [04/Aug/2025:00:00:06 +0800] "POST /api/reservation HTTP/1.1" 200 107 131018` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L12 | `************* [04/Aug/2025:00:00:08 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 316389` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L13 | `************* [04/Aug/2025:00:00:09 +0800] "POST /api/reservation HTTP/1.1" 200 107 130837` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L14 | `************ [04/Aug/2025:00:00:10 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 47 149079` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L15 | `************* [04/Aug/2025:00:00:12 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-09 HTTP/1.1" 200 700 284261` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L16 | `************* [04/Aug/2025:00:00:12 +0800] "GET /api/reservation/history HTTP/1.1" 200 3354 349203` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L17 | `************* [04/Aug/2025:00:00:13 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-08 HTTP/1.1" 200 2205 310827` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L18 | `************* [04/Aug/2025:00:00:14 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-07 HTTP/1.1" 200 3748 323416` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L19 | `************* [04/Aug/2025:00:00:14 +0800] "GET /api/patient/active HTTP/1.1" 200 803 134023` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L20 | `************* [04/Aug/2025:00:00:15 +0800] "GET /api/erhcCard/getQrCode?patientId=987858 HTTP/1.1" 200 422 162035` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L21 | `************* [04/Aug/2025:00:00:15 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 333279` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L22 | `************* [04/Aug/2025:00:00:15 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 213377` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L24 | `************* [04/Aug/2025:00:00:19 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-09 HTTP/1.1" 200 700 322264` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L26 | `************* [04/Aug/2025:00:00:23 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 315672` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L27 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 241318` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L28 | `************* [04/Aug/2025:00:00:23 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 331671` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L29 | `************* [04/Aug/2025:00:00:24 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 290581` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L30 | `********** [04/Aug/2025:00:00:24 +0800] "GET /open/config HTTP/1.0" 200 723 63972` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L31 | `************ [04/Aug/2025:00:00:25 +0800] "POST /open/wx/login HTTP/1.0" 200 95 442143` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L32 | `************* [04/Aug/2025:00:00:26 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 310297` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L34 | `************* [04/Aug/2025:00:00:27 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 291001` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L35 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 317447` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L36 | `************* [04/Aug/2025:00:00:27 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-07 HTTP/1.1" 200 3748 288849` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L37 | `************* [04/Aug/2025:00:00:29 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 278681` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L38 | `************* [04/Aug/2025:00:00:29 +0800] "GET /api/patient/active HTTP/1.0" 200 62 126568` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L39 | `************* [04/Aug/2025:00:00:29 +0800] "GET /open/config HTTP/1.1" 200 735 62955` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L40 | `************* [04/Aug/2025:00:00:29 +0800] "GET /api/patient/active HTTP/1.1" 200 735 126805` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L42 | `************* [04/Aug/2025:00:00:30 +0800] "GET /api/patient/menzhenBalance?patientId=1240879&id=1828 HTTP/1.1" 200 54 206644` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L43 | `************* [04/Aug/2025:00:00:31 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 310693` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L44 | `************ [04/Aug/2025:00:00:32 +0800] "GET /api/patient/active HTTP/1.0" 200 62 126492` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L45 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 277563` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L46 | `************* [04/Aug/2025:00:00:33 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 290249` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L47 | `************* [04/Aug/2025:00:00:34 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 291613` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L48 | `************* [04/Aug/2025:00:00:35 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 313718` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L49 | `************* [04/Aug/2025:00:00:35 +0800] "GET /api/patient/menzhenBalance?patientId=1240879&id=1828 HTTP/1.1" 200 54 200618` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L50 | `************* [04/Aug/2025:00:00:36 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-06 HTTP/1.1" 200 942 324632` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L51 | `************* [04/Aug/2025:00:00:36 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 282430` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L52 | `************* [04/Aug/2025:00:00:37 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-07 HTTP/1.1" 200 3748 278566` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L53 | `************* [04/Aug/2025:00:00:41 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 286824` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L54 | `************* [04/Aug/2025:00:00:42 +0800] "GET /api/patient/active HTTP/1.1" 200 735 132336` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L55 | `************* [04/Aug/2025:00:00:42 +0800] "GET /api/patient/menzhenBalance?patientId=1240879&id=1828 HTTP/1.1" 200 54 205511` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L58 | `************* [04/Aug/2025:00:00:44 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-09 HTTP/1.1" 200 700 305801` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L59 | `************* [04/Aug/2025:00:00:46 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-08 HTTP/1.1" 200 2205 316888` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L60 | `************* [04/Aug/2025:00:00:46 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 289476` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L61 | `************* [04/Aug/2025:00:00:46 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 322831` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L62 | `************* [04/Aug/2025:00:00:48 +0800] "GET /api/patient/active HTTP/1.1" 200 803 128846` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L63 | `************* [04/Aug/2025:00:00:48 +0800] "GET /api/erhcCard/getQrCode?patientId=987858 HTTP/1.1" 200 422 155258` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L65 | `************* [04/Aug/2025:00:00:48 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 210524` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L67 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 293407` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L68 | `************* [04/Aug/2025:00:00:52 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 318506` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L69 | `************ [04/Aug/2025:00:00:52 +0800] "GET /api/patient/active HTTP/1.1" 200 821 133783` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L70 | `************ [04/Aug/2025:00:00:53 +0800] "GET /api/erhcCard/getQrCode?patientId=1214546 HTTP/1.1" 200 469 160590` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L71 | `************* [04/Aug/2025:00:00:53 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 224154` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L72 | `************* [04/Aug/2025:00:00:53 +0800] "GET /api/reservation/history HTTP/1.1" 200 3354 360548` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L73 | `************* [04/Aug/2025:00:00:54 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-04 HTTP/1.1" 200 1753 608970` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L74 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 252006` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L75 | `************* [04/Aug/2025:00:00:55 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 304820` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L76 | `************ [04/Aug/2025:00:00:56 +0800] "GET /api/patient/active HTTP/1.1" 200 62 134716` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L77 | `************ [04/Aug/2025:00:00:56 +0800] "GET /api/patient/active HTTP/1.1" 200 62 135749` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L78 | `************ [04/Aug/2025:00:00:56 +0800] "GET /api/patient/active HTTP/1.1" 200 62 134738` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L79 | `************ [04/Aug/2025:00:00:57 +0800] "GET /api/patient/active HTTP/1.1" 200 62 134776` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L80 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 254803` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L81 | `************* [04/Aug/2025:00:00:57 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HTTP/1.1" 200 1525 284019` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L83 | `⟪ 102 characters skipped ⟫te=2025-08-04&days=7 HTTP/1.1" 200 11343 255129` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L84 | `************* [04/Aug/2025:00:01:01 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HTTP/1.1" 200 3332 294991` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L85 | `************* [04/Aug/2025:00:01:03 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-05 HTTP/1.1" 200 1443 323316` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L86 | `************* [04/Aug/2025:00:01:05 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-04 HTTP/1.1" 200 1753 332586` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L87 | `************* [04/Aug/2025:00:01:06 +0800] "GET /api/patient/active HTTP/1.1" 200 803 147046` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L88 | `************* [04/Aug/2025:00:01:06 +0800] "GET /api/erhcCard/getQrCode?patientId=987858 HTTP/1.1" 200 422 163242` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L89 | `************* [04/Aug/2025:00:01:06 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.1" 200 55 208578` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L90 | `************* [04/Aug/2025:00:01:06 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-04 HTTP/1.1" 200 1753 317397` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L91 | `36.184.164.163 [04/Aug/2025:00:01:07 +0800] "GET /open/schedule/tickets?doctorCode=577&date=2025-08-04&ampm=2 HTTP/1.1" 200 398 19508` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L92 | `36.184.164.163 [04/Aug/2025:00:01:07 +0800] "GET /api/patient/menzhenBalance?patientId=924159&id=3341 HTTP/1.1" 200 54 204943` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L93 | `49.118.18.30 [04/Aug/2025:00:01:08 +0800] "GET /open/config HTTP/1.1" 200 735 64186` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L94 | `49.118.18.30 [04/Aug/2025:00:01:08 +0800] "GET /api/patient/active HTTP/1.1" 200 677 129139` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L96 | `49.118.18.30 [04/Aug/2025:00:01:08 +0800] "GET /api/patient/menzhenBalance?patientId=835976&id=2577 HTTP/1.1" 200 55 212765` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L97 | `49.118.18.30 [04/Aug/2025:00:01:09 +0800] "POST /open/wx/login HTTP/1.1" 200 106 433492` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L99 | `49.118.18.30 [04/Aug/2025:00:01:09 +0800] "GET /api/patient/active HTTP/1.1" 200 677 133273` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L100 | `36.184.164.163 [04/Aug/2025:00:01:10 +0800] "POST /api/reservation HTTP/1.1" 200 359 549611` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L101 | `36.184.164.163 [04/Aug/2025:00:01:11 +0800] "GET /api/patient/menzhenBalance?patientId=924159&id=3341 HTTP/1.1" 200 54 214778` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L102 | `36.184.164.163 [04/Aug/2025:00:01:11 +0800] "GET /api/reservation/history HTTP/1.1" 200 9751 348492` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L103 | `36.184.106.237 [04/Aug/2025:00:01:11 +0800] "GET /api/patient/active HTTP/1.1" 200 803 145536` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L104 | `************* [04/Aug/2025:00:01:11 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-05 HTTP/1.1" 200 1443 306572` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L105 | `36.184.106.237 [04/Aug/2025:00:01:12 +0800] "GET /api/erhcCard/getQrCode?patientId=832549 HTTP/1.1" 200 422 156739` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L106 | `************* [04/Aug/2025:00:01:12 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-06 HTTP/1.1" 200 1599 327539` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L107 | `36.184.106.237 [04/Aug/2025:00:01:12 +0800] "GET /api/patient/menzhenBalance?patientId=832549&id=1634 HTTP/1.1" 200 56 214867` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L108 | `175.24.211.233 [04/Aug/2025:00:01:12 +0800] "GET /open/config HTTP/1.0" 200 723 66667` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L111 | `************* [04/Aug/2025:00:01:13 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-07 HTTP/1.1" 200 1443 306938` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L112 | `************* [04/Aug/2025:00:01:14 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-08 HTTP/1.1" 200 979 290994` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L113 | `************* [04/Aug/2025:00:01:14 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-05 HTTP/1.1" 200 1470 294525` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/access_log.2025-08-04.log` | L114 | `************* [04/Aug/2025:00:01:14 +0800] "GET /open/schedule/doctors?departmentCode=123&date=2025-08-09 HTTP/1.1" 200 979 337357` |

</details>

---

<think><details><summary>Thought Process</summary>我看到搜索结果找到了很多响应时间大于10秒的行。但现在我需要将这些结果保存到 slow.log 文件中。由于搜索结果显示有更多的结果可用，我需要使用命令行工具来完整地筛选并保存所有响应时间大于10000毫秒的行。

我应该使用运行终端命令的方式来实现这个操作，用grep命令筛选出响应时间大于10000的行并保存到slow.log文件中。</details></think>

---

现在我将把所有响应时间大于10秒（10000毫秒）的日志行保存到`slow.log`文件中：

---

```bash
grep -E ' (1[0-9]{4,}|[2-9][0-9]{4,})$' access_log.2025-08-04.log > slow.log
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy grep -E ' (1[0-9]{4,}|[2-9][0-9]{4,})$' access_log.2025-08-04.log
 > slow.log    grep -E ' (1[0-9]{4,}|[2-9][0-9]{4,})$' access_log.2025-08-04.log > slow.log
```

---

<think><details><summary>Thought Process</summary>看起来命令执行出现了一些问题。让我检查一下slow.log文件是否成功创建以及其内容。</details></think>

---

让我检查一下`slow.log`文件是否成功创建并查看其内容：

---

```bash
wc -l slow.log && head -5 slow.log
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔) wc -l slow.log && head -5 slow.log
       0 slow.log
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔)
```

---

<think><details><summary>Thought Process</summary>看起来slow.log文件是空的，这意味着grep命令可能没有正确执行。让我重新尝试这个命令，并检查一下原始日志文件的格式。让我先看看日志文件的几行来确认格式。</details></think>

---

文件是空的，让我重新检查日志格式并重新执行筛选命令：

---

```bash
head -3 access_log.2025-08-04.log
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔) head -3 access_log.2025-08-04.log
************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" ************97
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 345478
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 389992
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔)
```

---

<think><details><summary>Thought Process</summary>现在我可以看到日志格式了。日志的最后一个数字确实是响应时间。让我重新使用grep命令来筛选响应时间大于10000毫秒的行。我需要用更准确的正则表达式。</details></think>

---

现在我重新执行筛选命令，使用更精确的正则表达式：

---

```bash
grep -E ' [1-9][0-9]{4,}$' access_log.2025-08-04.log > slow.log
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔) grep -E ' [1-9][0-9]{4,}$' access_log.2025-08-04.log >
<1-9][0-9]{4,}$' access_log.2025-08-04.log > slow.log
```

---

<think><details><summary>Thought Process</summary>还是有问题。让我尝试用一个更直接的方法，使用awk来筛选最后一个字段（响应时间）大于10000的行。</details></think>

---

让我使用`awk`命令来更精确地筛选响应时间：

---

```bash
awk '$NF > 10000' access_log.2025-08-04.log > slow.log
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔) awk '$NF > 10000' access_log.2025-08-04.log > slow.log
<k '$NF > 10000' access_log.2025-08-04.log > slow.log
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔)
```

---

<think><details><summary>Thought Process</summary>现在命令成功执行了。让我检查slow.log文件的内容来确认筛选结果。</details></think>

---

现在让我检查`slow.log`文件的内容：

---

```bash
wc -l slow.log && echo "=== 前10行 ===" && head -10 slow.log
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔) wc -l slow.log && echo "=== 前10行 ===" && head -10 sl
<ow.log && echo "=== 前10行 ===" && head -10 slow.log
   61621 slow.log
=== 前10行 ===
************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" ************97
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 345478
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 389992
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/tickets?doctorCode=574&date=2025-08-11&ampm=1
 HTTP/1.1" 200 398 22551
************* [04/Aug/2025:00:00:03 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.
1" 200 55 201562
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 238800
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 385224
************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 234764
************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 263585
************* [04/Aug/2025:00:00:06 +0800] "POST /api/reservation HTTP/1.1" 200 107 131018
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔)
```

---

<think><details><summary>Thought Process</summary>很好！现在我看到slow.log文件已经成功创建，包含了61,621行响应时间大于10秒的日志记录。让我再显示一些统计信息，比如最慢的几个请求。</details></think>

---

让我再查看一些统计信息，包括最慢的请求：

---

```bash
echo "=== 最慢的10个请求 ===" && sort -k8 -nr slow.log | head -10
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔) echo "=== 最慢的10个请求 ===" && sort -k8 -nr slow.log
的10个请求 ===" && sort -k8 -nr slow.log | head -100
=== 最慢的10个请求 ===
************** [04/Aug/2025:12:17:27 +0800] "GET /img/login-background.jpg HTTP/1.1" 200 450191 576621
************** [04/Aug/2025:11:41:12 +0800] "GET /img/login-background.jpg HTTP/1.1" 200 450191 688581
************** [04/Aug/2025:10:16:23 +0800] "GET /img/login-background.jpg HTTP/1.1" 200 450191 540358
************* [04/Aug/2025:09:36:51 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 395751 576229
************* [04/Aug/2025:09:36:23 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 395751 845012
112.43.109.206 [04/Aug/2025:11:47:03 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 ***********832
60.13.165.36 [04/Aug/2025:08:55:19 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 306163 277821
49.119.198.147 [04/Aug/2025:12:14:53 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 306163 143883
49.118.90.227 [04/Aug/2025:11:30:30 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 306163 93532
49.118.85.47 [04/Aug/2025:00:20:47 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 306163 46691
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔)
```

---

<think><details><summary>Thought Process</summary>很好！现在我看到了最慢的请求。让我再查看一下响应时间的分布情况，看看大部分慢请求是在什么时间范围内。</details></think>

---

让我再查看一下响应时间分布和一些统计信息：

---

```bash
echo "=== 响应时间统计 ===" && awk '{print $NF}' slow.log | sort -n | awk 'BEGIN{count=0; sum=0} {count++; sum+=$1; if(count==1) min=$1} END{print "总数: " count; print "最小值: " min " ms (" min/1000 " 秒)"; print "最大值: " $1 " ms (" $1/1000 " 秒)"; print "平均值: " int(sum/count) " ms (" int(sum/count)/1000 " 秒)"}'
```

```
< int(sum/count) " ms (" int(sum/count)/1000 " 秒)"}'
=== 响应时间统计 ===
总数: 61621
 ms (10.04 秒)
 ms (11958 秒)88
平均值: 236890 ms (236.89 秒)
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔)
```

---

<think><details><summary>Thought Process</summary>我看到统计信息的输出格式有些混乱。让我重新执行一个更简单的命令来获得清晰的统计信息。</details></think>

---

让我用更简单的方式查看统计信息：

---

```bash
echo "=== slow.log 文件创建完成 ===" && echo "包含 $(wc -l < slow.log) 条响应时间超过10秒的请求" && echo "=== 最慢的请求响应时间 ===" && awk '{print $NF}' slow.log | sort -nr | head -1 | awk '{print $1/1000 " 秒"}'
```

```
<g | sort -nr | head -1 | awk '{print $1/1000 " 秒"}'
=== slow.log 文件创建完成 ===
包含    61621 条响应时间超过10秒的请求
=== 最慢的请求响应时间 ===
11958 秒
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔)
```

---

<think><details><summary>Thought Process</summary>太好了！现在任务已经完成。让我总结一下结果。</details></think>

---

## ✅ 任务完成！

已成功筛选出所有响应时间大于10秒的日志行并保存到`slow.log`文件中。

## 📊 筛选结果概览

- **文件位置**: `slow.log`
- **记录总数**: **61,621** 条慢请求记录
- **筛选条件**: 响应时间 > 10,000 毫秒（10秒）
- **最慢请求**: 响应时间达到 **11,958 秒**（约3.3小时）

## 🔍 发现的问题

从筛选结果可以看出系统存在严重的性能问题：

1. **高延迟请求数量大**: 61,621条超过10秒的请求
2. **极端响应时间**: 最慢请求达到近3.3小时
3. **主要慢请求类型**:
   - 图片资源加载 (`/img/login-background.jpg`, `/img/health_card_template.png`)
   - 住院费用查询 (`/api/zhuyuan/fee?days=7`)
   - 各种API调用响应缓慢

## 💡 建议

建议优先处理：
1. 图片资源的CDN优化
2. 数据库查询优化，特别是住院费用相关查询
3. 服务器性能调优
4. 缓存机制优化

`slow.log`文件现在可以用于进一步的性能分析和问题排查。

---

_**User**_

这份访问日志中的响应时间慢得不合理，有没有可能是这个时间不准确？

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户质疑访问日志中的响应时间可能不准确，这是一个很好的观察。让我分析一下这个问题：

1. 刚才我们看到最慢的请求响应时间达到了11958秒（约3.3小时），这确实非常不合理
2. 我需要仔细查看日志格式，确认最后一个数字确实是响应时间
3. 需要分析日志的格式和字段含义
4. 可能需要查看一些具体的日志行来理解格式

让我先读取一些日志行来分析格式。</details></think>

---

您的观察很有道理！让我仔细分析一下这个日志格式，确认最后一个字段是否真的是响应时间。

---

```bash
head -5 access_log.2025-08-04.log
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✔) head -5 access_log.2025-08-04.log
************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" ************97
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 345478
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 389992
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/tickets?doctorCode=574&date=2025-08-11&ampm=1
 HTTP/1.1" 200 398 22551
************* [04/Aug/2025:00:00:03 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.
1" 200 55 201562
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在我看到了日志格式。让我分析一下：

格式似乎是：
IP地址 [时间戳] "请求方法 URL HTTP/版本" 状态码 响应大小 最后一个数字

最后一个数字确实很大：127397, 345478, 389992, 22551, 201562

这些数字如果是响应时间（毫秒），确实不合理：
- 127397ms = 127.4秒
- 345478ms = 345.5秒  
- 389992ms = 390秒

但让我想想其他可能性：

1. 这可能是响应时间，但单位可能不是毫秒，而是微秒？
2. 这可能根本不是响应时间，而是其他字段，比如：
   - 请求大小
   - 某种ID
   - 字节传输量的累计
   - 连接时间
   - 其他度量

让我查看更多的日志行，特别是查看那些"最慢"的请求，看看能否发现规律。</details></think>

---

```bash
echo "=== 查看一些正常的日志行格式 ===" && grep -v "GET /img/" access_log.2025-08-04.log | head -10
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 查看一些正常的日志行格式 ===" && grep -v "GE
< -v "GET /img/" access_log.2025-08-04.log | head -10
=== 查看一些正常的日志行格式 ===
************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" ************97
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 345478
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 389992
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/tickets?doctorCode=574&date=2025-08-11&ampm=1
 HTTP/1.1" 200 398 22551
************* [04/Aug/2025:00:00:03 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.
1" 200 55 201562
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 238800
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 385224
************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 234764
223.116.64.250 [04/Aug/2025:00:00:04 +0800] "GET /open/config/bool/bool-wxxcx-yuyue-guahao HTTP/1.1" 200 54 
1555
************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 263585
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在我看到了更多的模式。让我仔细分析：

在最后一行：
`223.116.64.250 [04/Aug/2025:00:00:04 +0800] "GET /open/config/bool/bool-wxxcx-yuyue-guahao HTTP/1.1" 200 54 1555`

这里最后一个数字是1555，这个数字相对较小，如果是响应时间（毫秒），那就是1.555秒，这是相对合理的。

但是其他的数字如：127397, 345478, 389992 等，如果是毫秒的话就是127秒、345秒、390秒，这确实太慢了。

让我再查看一下那些我们认为"最慢"的请求，看看是否有其他线索。也让我查看一下这个日志格式是否符合标准的Apache/Nginx日志格式。

标准的Apache/Nginx访问日志格式通常是：
- Combined Log Format: IP - - [timestamp] "request" status size "referer" "user-agent"
- Common Log Format: IP - - [timestamp] "request" status size
- 或者可能有自定义格式

让我查看一些更多的样本来确认。</details></think>

---

```bash
echo "=== 查看那些'最慢'的请求具体内容 ===" && sort -k8 -nr access_log.2025-08-04.log | head -5
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 查看那些'最慢'的请求具体内容 ===" && sort -k
< && sort -k8 -nr access_log.2025-08-04.log | head -5
=== 查看那些'最慢'的请求具体内容 ===
************** [04/Aug/2025:12:17:27 +0800] "GET /img/login-background.jpg HTTP/1.1" 200 450191 576621
************** [04/Aug/2025:11:41:12 +0800] "GET /img/login-background.jpg HTTP/1.1" 200 450191 688581
************** [04/Aug/2025:10:16:23 +0800] "GET /img/login-background.jpg HTTP/1.1" 200 450191 540358
************* [04/Aug/2025:09:36:51 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 395751 576229
************* [04/Aug/2025:09:36:23 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 395751 845012
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在我开始怀疑这个日志格式了。让我仔细看：

对于那个图片请求：
`************** [04/Aug/2025:12:17:27 +0800] "GET /img/login-background.jpg HTTP/1.1" 200 450191 576621`

这里：
- IP: **************
- 时间: [04/Aug/2025:12:17:27 +0800]
- 请求: "GET /img/login-background.jpg HTTP/1.1"
- 状态码: 200
- 第二个数字: 450191
- 第三个数字: 576621

这看起来不像标准的Apache/Nginx日志格式。通常情况下：
- 状态码后面应该直接跟响应大小（字节）
- 然后可能是referer和user-agent

但这里有两个大数字：450191 和 576621

可能的解释：
1. 450191 可能是响应大小（字节），这对于一个图片文件来说是合理的（约450KB）
2. 576621 可能是响应时间，但单位可能不是毫秒

让我再查看一些小请求的例子，看看这个模式。</details></think>

---

```bash
echo "=== 查看一些快速请求（数值较小的） ===" && awk '$NF < 5000' access_log.2025-08-04.log | head -10
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 查看一些快速请求（数值较小的） ===" && awk '
<wk '$NF < 5000' access_log.2025-08-04.log | head -10
=== 查看一些快速请求（数值较小的） ===
223.116.64.250 [04/Aug/2025:00:00:04 +0800] "GET /open/config/bool/bool-wxxcx-yuyue-guahao HTTP/1.1" 200 54 
1555
************* [04/Aug/2025:00:00:16 +0800] "GET /open/config/bool/bool-wxxcx-yuyue-guahao HTTP/1.1" 200 54 2
462
************* [04/Aug/2025:00:00:19 +0800] "GET /open/schedule/indexedDepartments HTTP/1.1" 200 3243 622
175.24.19.178 [04/Aug/2025:00:00:26 +0800] "GET /api/enums HTTP/1.0" 200 1290 489
************* [04/Aug/2025:00:00:29 +0800] "GET /img/hesuan_baogao.jpg HTTP/1.1" 304 - 1842
************* [04/Aug/2025:00:00:42 +0800] "GET /open/schedule/indexedDepartments HTTP/1.1" 200 3243 763
************* [04/Aug/2025:00:00:44 +0800] "GET /open/config/bool/bool-wxxcx-yuyue-guahao HTTP/1.1" 200 54 1
567
************* [04/Aug/2025:00:00:48 +0800] "GET /open/schedule/indexedDepartments HTTP/1.1" 200 3243 667
************* [04/Aug/2025:00:00:48 +0800] "GET /open/schedule/indexedDepartments HTTP/1.1" 200 3243 640
************* [04/Aug/2025:00:00:58 +0800] "GET /open/schedule/indexedDepartments HTTP/1.1" 200 3243 767
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在我看到了一些更合理的数字！

看这些"快速"请求：
- 1555, 2462, 622, 489, 1842, 763, 1567, 667, 640, 767

这些数字如果是响应时间（毫秒），那就是：
- 1.555秒、2.462秒、0.622秒、0.489秒、1.842秒等

这些都是相对合理的响应时间！

所以我的分析可能是：
1. 最后一个字段确实是响应时间（毫秒）
2. 第二个字段（如450191）确实是响应大小（字节）
3. 但是系统确实存在一些极端慢的请求，可能有几个原因：

a) 图片资源请求确实很慢（可能网络问题、服务器IO问题）
b) 某些API查询（如/api/zhuyuan/fee?days=7）确实很慢，可能是数据库查询问题
c) 可能存在超时重试机制导致累计时间很长

让我再查看一些更多的证据来确认这个分析。特别是那个304状态码的例子很有趣。</details></think>

---

```bash
echo "=== 查看304状态码请求（缓存命中）===" && grep " 304 " access_log.2025-08-04.log | head -5
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 查看304状态码请求（缓存命中）===" && grep " 
< && grep " 304 " access_log.2025-08-04.log | head -5
=== 查看304状态码请求（缓存命中）===
************* [04/Aug/2025:00:00:29 +0800] "GET /img/hesuan_baogao.jpg HTTP/1.1" 304 - 1842
49.118.18.30 [04/Aug/2025:00:01:08 +0800] "GET /img/hesuan_baogao.jpg HTTP/1.1" 304 - 2088
39.144.197.15 [04/Aug/2025:00:02:21 +0800] "GET /img/hesuan_baogao.jpg HTTP/1.1" 304 - 3909
39.144.30.12 [04/Aug/2025:00:05:27 +0800] "GET /img/hesuan_baogao.jpg HTTP/1.1" 304 - 1951
************** [04/Aug/2025:00:06:36 +0800] "GET /img/health_card_template.png HTTP/1.1" 304 - 3518
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>很好！这提供了重要的线索：

304状态码的请求（缓存命中），响应时间都相对较快：
- 1842ms (1.8秒)
- 2088ms (2.1秒) 
- 3909ms (3.9秒)
- 1951ms (1.9秒)
- 3518ms (3.5秒)

这些都是相对合理的响应时间，特别是对于图片资源的缓存验证请求。

现在让我再查看一下那些极端慢的请求，看看是否有规律。</details></think>

---

```bash
echo "=== 分析日志格式和响应时间分布 ===" && echo "日志格式分析:" && echo "IP [时间] \"请求\" 状态码 响应大小(字节) 响应时间(毫秒)" && echo "" && echo "响应时间分布统计:" && awk '{print $NF}' access_log.2025-08-04.log | awk '{if($1<1000) a++; else if($1<5000) b++; else if($1<10000) c++; else if($1<60000) d++; else e++} END{print "< 1秒: " a; print "1-5秒: " b; print "5-10秒: " c; print "10-60秒: " d; print "> 60秒: " e}'
```

```
<10秒: " c; print "10-60秒: " d; print "> 60秒: " e}'
=== 分析日志格式和响应时间分布 ===
日志格式分析:
IP [时间] "请求" 状态码 响应大小(字节) 响应时间(毫秒)

响应时间分布统计:
< 1秒: 9784
1-5秒: 4397
5-10秒: 67
10-60秒: 914
> 60秒: 60707
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>



现在数据显示了一个很清楚的问题：

响应时间分布：
- < 1秒: 9,784 请求 (12.9%)
- 1-5秒: 4,397 请求 (5.8%)  
- 5-10秒: 67 请求 (0.1%)
- 10-60秒: 914 请求 (1.2%)
- > 60秒: 60,707 请求 (80.0%)

这意味着80%的请求响应时间超过60秒，这确实极不合理！

让我再深入分析一下那些超过60秒的请求，看看是否有特定的模式。</details></think>

---

```bash
echo "=== 分析超过60秒的请求类型 ===" && awk '$NF > 60000' access_log.2025-08-04.log | awk '{print $7}' | sort | uniq -c | sort -nr | head -10
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 分析超过60秒的请求类型 ===" && awk '$NF > 60
< '{print $7}' | sort | uniq -c | sort -nr | head -10
=== 分析超过60秒的请求类型 ===
60676 200
  30 500
   1 302
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>



看起来我的awk命令取的是状态码字段而不是URL字段。让我修正一下，我需要提取URL部分。在这种日志格式中，URL是在引号内的第二部分。</details></think>

---

```bash
echo "=== 分析超过60秒的请求URL类型 ===" && awk '$NF > 60000' access_log.2025-08-04.log | sed 's/.*"[A-Z]* \([^ ]*\).*/\1/' | sort | uniq -c | sort -nr | head -10
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 分析超过60秒的请求URL类型 ===" && awk '$NF >
 60000' access_log.2025-08-04.log | sed 's/.*"[A-Z]* \([^ ]*\).*/\1/' | sort | uniq -c | sort -nr | head -10
<^ ]*\).*/\1/' | sort | uniq -c | sort -nr | head -10
=== 分析超过60秒的请求URL类型 ===
60676 200
  30 500
   1 302
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>



我的正则表达式还是有问题。让我用一个更简单的方法，直接查看几个超过60秒的请求样本来分析。</details></think>

---

```bash
echo "=== 查看一些超过60秒的请求样本 ===" && awk '$NF > 60000' access_log.2025-08-04.log | head -10
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 查看一些超过60秒的请求样本 ===" && awk '$NF 
<k '$NF > 60000' access_log.2025-08-04.log | head -10
=== 查看一些超过60秒的请求样本 ===
************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" ************97
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 345478
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 389992
************* [04/Aug/2025:00:00:03 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.
1" 200 55 201562
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 238800
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 385224
************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 234764
************* [04/Aug/2025:00:00:04 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 263585
************* [04/Aug/2025:00:00:06 +0800] "POST /api/reservation HTTP/1.1" 200 107 131018
************* [04/Aug/2025:00:00:08 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-10 HT
TP/1.1" 200 1525 316389
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>



现在我看到了问题的模式！这些都是在凌晨00:00:00-00:00:08时间段的请求，包括：

1. `/api/patient/active` - 127.4秒
2. `/open/schedule/queryDoctorByDays` - 345.5秒 
3. `/open/schedule/doctors` - 390秒
4. `/api/patient/menzhenBalance` - 201.6秒
5. `/api/reservation` - 131秒

这些都是业务相关的API，不是静态资源，而且响应时间确实不合理地长。

让我检查一下是否这个问题主要发生在特定时间段，这可能暗示：
1. 系统在某个时间段负载很高
2. 可能是定时任务或批处理导致的
3. 数据库性能问题
4. 或者确实是日志记录有问题

让我检查时间分布。</details></think>

---

```bash
echo "=== 分析响应时间和时间段的关系 ===" && awk '$NF > 60000' access_log.2025-08-04.log | awk '{print substr($4,2,2)}' | sort | uniq -c | sort -nr
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 分析响应时间和时间段的关系 ===" && awk '$NF 
<'{print substr($4,2,2)}' | sort | uniq -c | sort -nr
=== 分析响应时间和时间段的关系 ===
55629 GE
5078 PO
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>



我的awk脚本取错了字段。让我修正：</details></think>

---

```bash
echo "=== 分析超过60秒请求的时间分布 ===" && awk '$NF > 60000' access_log.2025-08-04.log | awk '{print substr($4,14,2)}' | sort | uniq -c | sort -nr
```

```
<{print substr($4,14,2)}' | sort | uniq -c | sort -nr
=== 分析超过60秒请求的时间分布 ===
60707 
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>



字段提取还是有问题。让我用一个更直接的方法：</details></think>

---

```bash
echo "=== 现在我怀疑日志格式或记录方式有问题 ===" && echo "让我们检查这个可能的原因:" && echo "1. 查看正常响应时间的请求:" && awk '$NF < 5000' access_log.2025-08-04.log | wc -l && echo "2. 查看异常响应时间的请求:" && awk '$NF > 60000' access_log.2025-08-04.log | wc -l
```

```
< awk '$NF > 60000' access_log.2025-08-04.log | wc -l
=== 现在我怀疑日志格式或记录方式有问题 ===
让我们检查这个可能的原因:
1. 查看正常响应时间的请求:
   14181
2. 查看异常响应时间的请求:
   60707
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>



## 结论

基于我的分析，您的怀疑是**非常合理的**！这个访问日志中的响应时间数据确实存在问题：

### 🚨 发现的异常情况：

1. **极度不合理的响应时间分布**：
   - 80% (60,707个) 的请求响应时间超过60秒
   - 最长响应时间达到11,958秒（约3.3小时）
   - 这种分布在任何正常的Web服务中都是不可能的

2. **时间单位可能不正确**：
   - 如果最后一个字段是响应时间，单位可能不是毫秒
   - 可能是微秒（microseconds）而不是毫秒（milliseconds）
   - 也可能是其他计量单位

3. **可能的解释**：
   
   a) **单位错误**：如果是微秒为单位，那么：
   - 127397微秒 = 127.4毫秒（合理）
   - 345478微秒 = 345.5毫秒（合理）
   - 11958000微秒 = 11.96秒（相对合理）

   b) **字段含义错误**：最后一个字段可能不是响应时间，而是：
   - 请求处理的CPU时间
   - 某种累计计数器
   - 连接时间或其他度量

   c) **日志配置问题**：服务器的日志格式配置可能有误

### 💡 建议


我需要仔细验证日志数据的准确性。单位转换和字段定义可能是关键问题。通过检查服务器配置和文档，我可以揭示这些异常响应时间的真实原因。微秒和毫秒的单位差异可能导致了这些看似不合理的数值。</details></think>

---

## 🎯 您的观察非常敏锐！

基于我的详细分析，**这个访问日志中的响应时间确实不准确**，或者至少存在严重的理解偏差。

## 📊 问题证据

我发现了以下异常情况：

### 1. 极度不合理的响应时间分布
```
< 1秒:   9,784 请求 (12.9%)
1-5秒:   4,397 请求 (5.8%)  
5-10秒:    67 请求 (0.1%)
10-60秒:  914 请求 (1.2%)
> 60秒: 60,707 请求 (80.0%)
```

**80%的请求响应时间超过60秒，这在任何正常的Web服务中都是不可能的！**

### 2. 极端的响应时间值
- 最慢请求：**11,958,000** (如果是毫秒 = 3.3小时)
- 普通API请求：200-400秒响应时间
- 这些数值完全不符合实际Web服务性能

## 🔍 可能的原因

### 最可能：**单位错误**
如果最后一个字段是**微秒(microseconds)**而不是毫秒：

- `127397` 微秒 = **127.4 毫秒** ✅ 合理
- `345478` 微秒 = **345.5 毫秒** ✅ 合理  
- `11958000` 微秒 = **11.96 秒** ✅ 相对合理

### 其他可能性：
1. **字段含义错误**：可能不是响应时间，而是其他度量值
2. **日志配置问题**：服务器日志格式配置有误
3. **累计时间**：可能包含了连接时间、队列时间等

## 💡 结论

您的怀疑是**完全正确的**！建议：

1. **检查服务器日志配置**，确认字段含义和单位
2. **如果是微秒为单位**，那么将`slow.log`中的值除以1000，大部分请求实际响应时间是合理的
3. **重新生成慢查询日志**，使用正确的阈值（如10秒 = 10,000,000微秒）

这是一个很好的提醒：**在分析日志数据时，首先要确认数据格式和单位的准确性**！

---

_**User**_

我找到日志配置的格式，确认一下`%D`代表的含义

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户找到了日志配置文件，并且特别指出了 `%D` 这个格式。让我查看这个配置文件，并且确认 `%D` 在 Tomcat access log 格式中的含义。

从配置文件中我可以看到：
```
pattern: "%a %t \"%r\" %s %b %D"
```

这里的格式对应了我们之前看到的日志格式：
- `%a` - 远程IP地址
- `%t` - 时间戳
- `%r` - 请求行（方法、URL、协议）
- `%s` - HTTP状态码
- `%b` - 响应大小（字节）
- `%D` - 这就是我们需要确认的字段

在 Tomcat 的 access log 格式中，`%D` 代表的是请求处理时间，单位是**微秒（microseconds）**。这就解释了为什么我们看到的数值这么大！

这证实了我之前的猜测是正确的 - 最后一个字段确实是响应时间，但单位是微秒而不是毫秒。</details></think>

---

太好了！您找到了关键信息！让我确认一下Tomcat访问日志中`%D`的具体含义。

---

Read file: ruoyi-admin/src/main/resources/application-prod.yml

---

<think><details><summary>Thought Process</summary>现在我可以确认日志格式了！

pattern: "%a %t \"%r\" %s %b %D"

根据 Tomcat 官方文档，这些格式符号的含义是：
- %a - 远程IP地址
- %t - 时间戳 [时间]
- %r - 请求行（完整的HTTP请求）
- %s - HTTP状态码
- %b - 响应大小（字节数）
- %D - 处理请求的时间，单位是**微秒**（microseconds）

这就完全解释了我们之前看到的"异常"响应时间！实际上：
- 127397 微秒 = 127.4 毫秒
- 345478 微秒 = 345.5 毫秒
- 11958000 微秒 = 11.96 秒

这样的响应时间就完全合理了！

让我跟用户确认这个发现，并重新生成正确的慢查询日志。</details></think>

---

## 🎯 确认！问题找到了！

根据您提供的配置文件，**`%D` 确实代表响应时间，但单位是微秒（microseconds）**！

### 📋 日志格式解析
```yaml
pattern: "%a %t \"%r\" %s %b %D"
```

对应字段含义：
- `%a` - 远程IP地址
- `%t` - 时间戳 `[04/Aug/2025:00:00:00 +0800]`
- `%r` - 请求行 `"GET /api/patient/active HTTP/1.1"`
- `%s` - HTTP状态码 `200`
- `%b` - 响应大小 `714` (字节)
- `%D` - **处理时间，单位：微秒(microseconds)** ⭐

### 🔄 重新计算真实响应时间

之前的"异常"数据现在变得合理了：

```bash
127397 微秒 = 127.4 毫秒 ✅
345478 微秒 = 345.5 毫秒 ✅  
11958000 微秒 = 11.96 秒 ⚠️ (这确实是慢请求)
```

让我重新生成正确的慢查询日志（响应时间 > 10秒 = 10,000,000微秒）：

---

```bash
echo "=== 重新生成真正的慢查询日志（>10秒=10,000,000微秒） ===" && awk '$NF > 10000000' access_log.2025-08-04.log > slow_correct.log && echo "慢查询数量: $(wc -l < slow_correct.log)" && echo "=== 最慢的5个请求 ===" && sort -k7 -nr slow_correct.log | head -5
```

```
<请求 ===" && sort -k7 -nr slow_correct.log | head -5
=== 重新生成真正的慢查询日志（>10秒=10,000,000微秒） ===
慢查询数量:        6
=== 最慢的5个请求 ===
************ [04/Aug/2025:09:45:57 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 39 10143718
************** [04/Aug/2025:11:24:55 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 77 11547410
************** [04/Aug/2025:10:21:08 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 107 11201785
************* [04/Aug/2025:09:46:24 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 39 10147121
************* [04/Aug/2025:09:46:16 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 39 10151379
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在我看到了真正的慢查询！让我再计算一下这些真实的响应时间：

- 11547410 微秒 = 11.55 秒
- 11201785 微秒 = 11.20 秒  
- 10151379 微秒 = 10.15 秒
- 10147121 微秒 = 10.15 秒
- 10143718 微秒 = 10.14 秒

这些响应时间现在看起来是合理的慢查询了！主要是两种API：
1. `/api/erhcCard/getOrRegister` - 电子健康卡获取或注册
2. `/api/weixinMip/createOrder/v2` - 微信小程序创建订单

让我做一些进一步的分析。</details></think>

---

```bash
echo "=== 真实慢查询分析 ===" && echo "真实响应时间转换（微秒→秒）：" && awk '$NF > 10000000' access_log.2025-08-04.log | awk '{printf "%.2f秒: %s\n", $NF/1000000, $0}' && echo "" && echo "=== 响应时间分布重新统计 ===" && awk '{us=$NF; if(us<100000) a++; else if(us<500000) b++; else if(us<1000000) c++; else if(us<3000000) d++; else if(us<10000000) e++; else f++} END{printf "< 0.1秒: %d\n0.1-0.5秒: %d\n0.5-1秒: %d\n1-3秒: %d\n3-10秒: %d\n> 10秒: %d\n", a,b,c,d,e,f}' access_log.2025-08-04.log
```

```
<10秒: %d\n", a,b,c,d,e,f}' access_log.2025-08-04.log
=== 真实慢查询分析 ===
真实响应时间转换（微秒→秒）：
10.14秒: ************ [04/Aug/2025:09:45:57 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 39 101437
18
10.15秒: ************* [04/Aug/2025:09:46:16 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 39 10151
379
10.15秒: ************* [04/Aug/2025:09:46:24 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 39 10147
121
11.20秒: ************** [04/Aug/2025:10:21:08 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 107 1
1201785
11.96秒: ************** [04/Aug/2025:10:54:14 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1587 
11957988
11.55秒: ************** [04/Aug/2025:11:24:55 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 77 11
547410

=== 响应时间分布重新统计 ===
< 0.1秒: 20946
0.1-0.5秒: 51921
0.5-1秒: 1694
1-3秒: 1196
3-10秒: 106
> 10秒: 6
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在这个统计看起来就非常合理了！让我总结一下我们的发现：

1. **日志格式确认**：`%D` 确实是响应时间，单位是微秒
2. **真实慢查询**：只有6个请求超过10秒，这是合理的
3. **响应时间分布**：
   - < 0.1秒: 20,946 (27.6%)
   - 0.1-0.5秒: 51,921 (68.4%) 
   - 0.5-1秒: 1,694 (2.2%)
   - 1-3秒: 1,196 (1.6%)
   - 3-10秒: 106 (0.1%)
   - > 10秒: 6 (0.008%)

这个分布看起来就非常正常了！大部分请求在0.5秒以内完成，只有极少数请求超过10秒。

主要的慢查询问题集中在：
1. `POST /api/erhcCard/getOrRegister` - 电子健康卡相关API
2. `POST /api/weixinMip/createOrder/v2` - 微信小程序订单创建API

这些都是比较重的业务操作，10秒的响应时间虽然慢但不是完全不合理的。</details></think>

---

## ✅ 问题完全解决！

感谢您找到了配置文件！现在我们有了准确的答案：

### 🔍 **`%D` 的含义确认**
**`%D` = 处理请求的时间，单位：微秒(microseconds)**

### 📊 **真实的性能数据**

重新分析后，系统性能实际上相当健康：

#### 响应时间分布（正确）：
```
< 0.1秒:  20,946 请求 (27.6%) ✅
0.1-0.5秒: 51,921 请求 (68.4%) ✅ 
0.5-1秒:   1,694 请求 (2.2%)  ✅
1-3秒:     1,196 请求 (1.6%)  ⚠️
3-10秒:      106 请求 (0.1%)  ⚠️
> 10秒:        6 请求 (0.008%) 🚨
```

#### 真正的慢查询（>10秒）只有6个：
```
10.14秒: POST /api/erhcCard/getOrRegister
10.15秒: POST /api/erhcCard/getOrRegister  
10.15秒: POST /api/erhcCard/getOrRegister
11.20秒: POST /api/weixinMip/createOrder/v2
11.55秒: POST /api/weixinMip/createOrder/v2
11.96秒: POST /api/weixinMip/createOrder/v2
```

### 🎯 **关键发现**

1. **96%的请求在0.5秒内完成** - 性能良好！
2. **真正慢的API**：
   - 电子健康卡注册/获取 (`erhcCard/getOrRegister`)
   - 微信小程序订单创建 (`weixinMip/createOrder/v2`)

### 💡 **优化建议**

现在可以针对性地优化：
1. **重点关注**：电子健康卡和订单创建的API性能
2. **数据库查询优化**：这两个API可能涉及复杂的数据库操作
3. **第三方接口优化**：可能调用了外部服务导致延迟

**原来的问题完全是单位理解错误造成的！** 系统实际性能比我们初次分析的要好得多。

---

_**User**_

调整一下响应时间的单位，改为毫秒吧

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要将日志配置中的响应时间单位从微秒（%D）改为毫秒。根据Tomcat的访问日志格式，我需要检查是否有其他的模式变量可以用来表示毫秒为单位的响应时间。

根据Tomcat的访问日志格式：
- %D = 处理请求的时间（微秒）
- %T = 处理请求的时间（秒）

实际上，标准的Tomcat AccessLogValve并没有直接支持毫秒单位的响应时间格式。通常的做法是：
1. 使用 %D（微秒）然后在分析时除以1000
2. 使用 %T（秒）但这会丢失精度
3. 自定义日志格式或使用其他日志记录方式

让我查看当前的配置文件，然后建议最佳的解决方案。</details></think>

---

好的，我来帮您修改日志配置，将响应时间单位改为毫秒。让我先查看当前的配置文件：

---

Read file: ruoyi-admin/src/main/resources/application-prod.yml

---

<think><details><summary>Thought Process</summary>查看了配置文件，当前使用的是 `%D`（微秒）。不幸的是，Tomcat的标准AccessLogValve没有直接支持毫秒单位的格式变量。可用的时间相关变量有：

- `%D` - 处理请求的时间（微秒）
- `%T` - 处理请求的时间（秒）

没有 `%M` 或类似的毫秒格式。

有几种解决方案：
1. 保持 `%D`（微秒），然后在日志分析时进行转换
2. 使用自定义的日志格式或第三方库
3. 在应用层面添加自定义的响应时间记录

最实用的方案是保持现在的 `%D` 格式，但创建一个脚本或工具来转换单位。不过，如果用户坚持要在配置文件中修改，我可以建议一些替代方案。</details></think>

---

我需要告知您一个重要信息：**Tomcat的标准访问日志格式中没有直接支持毫秒单位的响应时间变量。**

## 🔍 可用的时间格式选项

Tomcat AccessLogValve支持的时间相关格式：
- `%D` - 响应时间（微秒）⭐ 当前使用
- `%T` - 响应时间（秒，整数）

## 💡 解决方案选择

### 方案1：保持当前格式，创建转换工具 ⭐ 推荐
保持`%D`格式，我可以为您创建一个脚本来实时转换显示：

---

```bash
cat > convert_log_to_ms.sh << 'EOF'
#!/bin/bash
# 将访问日志中的微秒转换为毫秒显示

if [ $# -eq 0 ]; then
    echo "用法: $0 <日志文件> [选项]"
    echo "选项:"
    echo "  --tail     实时监控日志"
    echo "  --slow N   只显示响应时间超过N毫秒的请求"
    exit 1
fi

LOG_FILE="$1"
SLOW_THRESHOLD=""

if [ "$2" = "--tail" ]; then
    tail -f "$LOG_FILE" | awk '{
        ms = $NF / 1000
        $NF = sprintf("%.1fms", ms)
        print $0
    }'
elif [ "$2" = "--slow" ] && [ -n "$3" ]; then
    SLOW_THRESHOLD="$3"
    awk -v threshold="$SLOW_THRESHOLD" '{
        ms = $NF / 1000
        if (ms > threshold) {
            $NF = sprintf("%.1fms", ms)
            print $0
        }
    }' "$LOG_FILE"
else
    awk '{
        ms = $NF / 1000
        $NF = sprintf("%.1fms", ms)
        print $0
    }' "$LOG_FILE"
fi
EOF

chmod +x convert_log_to_ms.sh
echo "✅ 已创建日志转换脚本: convert_log_to_ms.sh"
```

```
<\necho "✅ 已创建日志转换脚本: convert_log_to_ms.sh"
✅ 已创建日志转换脚本: convert_log_to_ms.sh
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在我已经创建了一个转换脚本。让我演示一下如何使用它：</details></think>

---

现在让我演示一下这个转换脚本的使用：

---

```bash
echo "=== 演示：转换后的日志格式 ===" && ./convert_log_to_ms.sh access_log.2025-08-04.log | head -5
```

```
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗) echo "=== 演示：转换后的日志格式 ===" && ./convert_log
<ert_log_to_ms.sh access_log.2025-08-04.log | head -5
=== 演示：转换后的日志格式 ===
************* [04/Aug/2025:00:00:00 +0800] "GET /api/patient/active HTTP/1.1" 200 714 127.4ms
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=108&date=202
5-08-04&days=7 HTTP/1.1" 200 11343 345.5ms
************* [04/Aug/2025:00:00:02 +0800] "GET /open/schedule/doctors?departmentCode=108&date=2025-08-04 HT
TP/1.1" 200 3332 390.0ms
************* [04/Aug/2025:00:00:03 +0800] "GET /open/schedule/tickets?doctorCode=574&date=2025-08-11&ampm=1
 HTTP/1.1" 200 398 22.6ms
************* [04/Aug/2025:00:00:03 +0800] "GET /api/patient/menzhenBalance?patientId=987858&id=2930 HTTP/1.
1" 200 55 201.6ms
(base) ~/Developer/Projects/zhangyi-xjetyy (master ✗)
```

---

<think><details><summary>Thought Process</summary>现在我演示一下其他功能，比如只显示慢请求：</details></think>

---

```bash
echo "=== 演示：只显示响应时间超过1000毫秒的请求 ===" && ./convert_log_to_ms.sh access_log.2025-08-04.log --slow 1000
```

```


************** [04/Aug/2025:09:46:42 +0800] "GET /api/erhcCard/getQrCode?patientId=1241986 HTTP/1.1" 200 422
 2640.2ms
110.152.19.32 [04/Aug/2025:09:46:42 +0800] "GET /api/erhcCard/getQrCode?patientId=1031114 HTTP/1.1" 200 422 
1714.8ms
************* [04/Aug/2025:09:46:44 +0800] "POST /api/erhcCard/bind HTTP/1.1" ************.0ms
110.154.64.231 [04/Aug/2025:09:47:14 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.6ms
110.154.64.231 [04/Aug/2025:09:47:32 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.4ms
110.157.87.61 [04/Aug/2025:09:48:01 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.0ms
************** [04/Aug/2025:09:48:43 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1881.1ms
39.144.197.21 [04/Aug/2025:09:48:59 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
************** [04/Aug/2025:09:49:02 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1421.9ms
39.144.197.21 [04/Aug/2025:09:49:06 +0800] "GET /api/report/mzlis HTTP/1.1" ************.0ms
39.144.197.21 [04/Aug/2025:09:49:09 +0800] "GET /api/erhcCard/getQrCode?patientId=1238279 HTTP/1.1" 200 465 
1549.8ms
112.43.109.206 [04/Aug/2025:09:50:04 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 3561 1932.9ms
************* [04/Aug/2025:09:52:14 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2501 1990.5ms
39.144.197.38 [04/Aug/2025:09:53:38 +0800] "GET /api/erhcCard/getQrCode?patientId=1241933 HTTP/1.1" 200 474 
2065.8ms
************** [04/Aug/2025:09:54:05 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1340 1846.0ms
120.71.194.186 [04/Aug/2025:09:54:49 +0800] "GET /api/erhcCard/getQrCode?patientId=1241960 HTTP/1.1" 200 465
 1430.2ms
116.178.171.93 [04/Aug/2025:09:55:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1958.5ms
************* [04/Aug/2025:09:55:35 +0800] "GET /api/erhcCard/getQrCode?patientId=1242024 HTTP/1.1" 200 422 
1374.2ms
************** [04/Aug/2025:09:55:35 +0800] "GET /api/erhcCard/getQrCode?patientId=1240392 HTTP/1.1" 200 422
 2494.4ms
************* [04/Aug/2025:09:55:36 +0800] "GET /api/erhcCard/getQrCode?patientId=1241197 HTTP/1.1" 200 509 
1488.4ms
************* [04/Aug/2025:09:55:43 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.6ms
************ [04/Aug/2025:09:56:18 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1026 1798.9ms
************* [04/Aug/2025:09:56:45 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 77 1035.6ms
************** [04/Aug/2025:09:57:02 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 6640 1103.5ms
*************** [04/Aug/2025:09:59:07 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
************** [04/Aug/2025:09:59:51 +0800] "GET /api/erhcCard/getQrCode?patientId=799468 HTTP/1.1" 200 419 
1037.9ms
************* [04/Aug/2025:10:00:16 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 4402 3644.1ms
************** [04/Aug/2025:10:00:35 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1858.2ms
************6 [04/Aug/2025:10:00:56 +0800] "GET /open/schedule/doctors?departmentCode=103&date=2025-08-04 HT
TP/1.1" 200 6411 1040.7ms
************6 [04/Aug/2025:10:00:56 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=103&date=202
5-08-04&days=7 HTTP/1.1" 200 35156 1056.2ms
*************** [04/Aug/2025:10:00:58 +0800] "GET /api/erhcCard/getQrCode?patientId=1242092 HTTP/1.1" 200 42
2 1481.3ms
************** [04/Aug/2025:10:01:19 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1765.3ms
************** [04/Aug/2025:10:01:30 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1806.7ms
************** [04/Aug/2025:10:02:06 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1853.3ms
39.144.196.21 [04/Aug/2025:10:02:37 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1534 1973.8ms
39.144.197.20 [04/Aug/2025:10:02:42 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1316.5ms
116.178.122.235 [04/Aug/2025:10:02:55 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1917.3ms
116.178.122.235 [04/Aug/2025:10:03:00 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1299.3ms
*************** [04/Aug/2025:10:03:01 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1709 1984.1ms
116.178.122.235 [04/Aug/2025:10:03:13 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1423.9ms
116.178.122.235 [04/Aug/2025:10:03:21 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1787.9ms
39.144.196.21 [04/Aug/2025:10:03:37 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1534 1870.9ms
116.178.122.235 [04/Aug/2025:10:03:47 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.0ms
*************** [04/Aug/2025:10:03:53 +0800] "GET /api/erhcCard/getQrCode?patientId=1241600 HTTP/1.1" 200 46
8 2116.4ms
116.178.122.235 [04/Aug/2025:10:04:18 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1884.0ms
116.178.122.235 [04/Aug/2025:10:04:22 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1793.1ms
112.43.125.11 [04/Aug/2025:10:05:27 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
112.43.125.11 [04/Aug/2025:10:06:17 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************** [04/Aug/2025:10:06:18 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.6ms
39.144.196.21 [04/Aug/2025:10:07:05 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1534 1892.7ms
39.144.196.21 [04/Aug/2025:10:07:21 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1534 1836.6ms
112.43.85.6 [04/Aug/2025:10:07:41 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.0ms
39.144.33.253 [04/Aug/2025:10:07:57 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.7ms
110.157.71.49 [04/Aug/2025:10:08:02 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" ************.5ms
************** [04/Aug/2025:10:08:26 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
************** [04/Aug/2025:10:08:47 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.3ms
114.86.24.218 [04/Aug/2025:10:08:52 +0800] "POST /api/erhcCard/bind HTTP/1.1" ************.1ms
************3 [04/Aug/2025:10:09:11 +0800] "GET /api/report/mzlis HTTP/1.1" ************.1ms
39.144.196.21 [04/Aug/2025:10:09:12 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1534 2008.3ms
39.144.196.21 [04/Aug/2025:10:09:27 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1534 2051.9ms
************3 [04/Aug/2025:10:09:39 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
************3 [04/Aug/2025:10:09:57 +0800] "GET /api/report/mzlis HTTP/1.1" ************.1ms
************** [04/Aug/2025:10:11:11 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3787 4734.2ms
124.88.1.156 [04/Aug/2025:10:12:07 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1351 2740.8ms
************** [04/Aug/2025:10:12:43 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
117.190.91.138 [04/Aug/2025:10:12:49 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.4ms
117.190.91.138 [04/Aug/2025:10:12:59 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1001 1910.4ms
117.190.91.138 [04/Aug/2025:10:13:17 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1001 1822.6ms
117.190.91.138 [04/Aug/2025:10:13:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1001 1951.8ms
39.144.197.38 [04/Aug/2025:10:13:34 +0800] "GET /api/erhcCard/getQrCode?patientId=1161425 HTTP/1.1" 200 471 
1319.7ms
*************** [04/Aug/2025:10:13:50 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.8ms
117.190.91.138 [04/Aug/2025:10:13:56 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1001 1859.3ms
************* [04/Aug/2025:10:14:41 +0800] "GET /api/erhcCard/getQrCode?patientId=1232441 HTTP/1.1" 200 422 
1352.8ms
************* [04/Aug/2025:10:15:03 +0800] "GET /api/erhcCard/getQrCode?patientId=1232441 HTTP/1.1" 200 422 
2194.9ms
************** [04/Aug/2025:10:15:10 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46112 2196.4ms
************* [04/Aug/2025:10:15:22 +0800] "GET /api/erhcCard/getQrCode?patientId=1240231 HTTP/1.1" 200 422 
1135.8ms
************** [04/Aug/2025:10:15:39 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1850.0ms
************** [04/Aug/2025:10:15:55 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1426.3ms
*************** [04/Aug/2025:10:16:21 +0800] "GET /api/patient/active HTTP/1.1" ************.4ms
************* [04/Aug/2025:10:16:21 +0800] "GET /api/erhcCard/getQrCode?patientId=1240722 HTTP/1.1" 200 422 
1726.3ms
************** [04/Aug/2025:10:16:21 +0800] "GET /login HTTP/1.1" 200 3396 1070.3ms
************* [04/Aug/2025:10:16:24 +0800] "POST /open/wx/login HTTP/1.1" ************.7ms
************* [04/Aug/2025:10:16:40 +0800] "GET /api/erhcCard/getQrCode?patientId=1104142 HTTP/1.1" 200 471 
2207.2ms
************** [04/Aug/2025:10:16:45 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" ************.3ms
************* [04/Aug/2025:10:16:45 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2501 1888.4ms
************** [04/Aug/2025:10:16:49 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1871.5ms
************** [04/Aug/2025:10:17:00 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1438.9ms
************ [04/Aug/2025:10:17:02 +0800] "POST /api/erhcCard/bind HTTP/1.1" ************.5ms
************** [04/Aug/2025:10:17:11 +0800] "GET /open/schedule/indexedDepartments HTTP/1.1" 200 3243 4942.5
ms
************** [04/Aug/2025:10:17:17 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1832.7ms
************** [04/Aug/2025:10:17:19 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" ************.0ms
************ [04/Aug/2025:10:17:21 +0800] "GET /api/erhcCard/getQrCode?patientId=920437 HTTP/1.1" 200 422 26
15.4ms
************** [04/Aug/2025:10:17:21 +0800] "GET /api/erhcCard/getQrCode?patientId=1240922 HTTP/1.1" 200 474
 3014.7ms
************* [04/Aug/2025:10:17:21 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" ************.9ms
*************** [04/Aug/2025:10:17:21 +0800] "GET /api/erhcCard/getQrCode?patientId=1241688 HTTP/1.1" 200 42
2 1257.5ms
************** [04/Aug/2025:10:17:23 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1845.3ms
************** [04/Aug/2025:10:17:27 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1332.4ms
*************** [04/Aug/2025:10:17:31 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1709 1378.0ms
************** [04/Aug/2025:10:17:38 +0800] "GET /api/erhcCard/getQrCode?patientId=1240922 HTTP/1.1" 200 474
 3026.0ms
************* [04/Aug/2025:10:17:43 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2600 1265.8ms
************** [04/Aug/2025:10:17:43 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1458.4ms
************** [04/Aug/2025:10:18:03 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1330.5ms
************* [04/Aug/2025:10:18:08 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2600 1269.3ms
************** [04/Aug/2025:10:18:10 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1399.4ms
39.144.196.62 [04/Aug/2025:10:18:25 +0800] "GET /api/erhcCard/getQrCode?patientId=736147 HTTP/1.1" 200 471 3
184.2ms
************* [04/Aug/2025:10:18:30 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 1763.8ms
************** [04/Aug/2025:10:18:30 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1894.6ms
************** [04/Aug/2025:10:18:43 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1238.0ms
************* [04/Aug/2025:10:18:46 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2600 1268.0ms
*************** [04/Aug/2025:10:18:55 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1709 1301.9ms
************** [04/Aug/2025:10:19:02 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2297 1824.0ms
************* [04/Aug/2025:10:19:38 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2600 1910.8ms
************** [04/Aug/2025:10:19:43 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1286.1ms
*************** [04/Aug/2025:10:19:48 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1709 1337.8ms
221.7.8.190 [04/Aug/2025:10:20:14 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 19707 1005.5ms
************** [04/Aug/2025:10:20:22 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1936.2ms
************** [04/Aug/2025:10:20:22 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2908 1903.1ms
************** [04/Aug/2025:10:20:27 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1808.3ms
************** [04/Aug/2025:10:20:41 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1849.2ms
************* [04/Aug/2025:10:20:46 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 1770.6ms
************** [04/Aug/2025:10:21:08 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 107 11201.8ms
************** [04/Aug/2025:10:21:18 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 2029.9ms
************* [04/Aug/2025:10:21:19 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 33572 2157.4ms
************* [04/Aug/2025:10:21:34 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 33572 1960.0ms
************* [04/Aug/2025:10:21:41 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 33572 1411.0ms
************** [04/Aug/2025:10:21:45 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 77 1073.8ms
************** [04/Aug/2025:10:22:00 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 1780.1ms
************* [04/Aug/2025:10:22:08 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 1945.0ms
*********** [04/Aug/2025:10:22:16 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1011 1896.7ms
************** [04/Aug/2025:10:22:17 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 1567.3ms
************* [04/Aug/2025:10:22:19 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 33572 2041.1ms
************* [04/Aug/2025:10:22:25 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1387.9ms
************* [04/Aug/2025:10:22:30 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1803.8ms
*********** [04/Aug/2025:10:22:35 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1011 1897.7ms
************* [04/Aug/2025:10:22:41 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 33572 1340.9ms
************* [04/Aug/2025:10:22:45 +0800] "GET /api/erhcCard/getQrCode?patientId=1102211 HTTP/1.1" 200 509 
2114.0ms
************** [04/Aug/2025:10:22:56 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2638 1029.5ms
************** [04/Aug/2025:10:23:02 +0800] "POST /ph/medical-record-revenue-statistics/calculate-range HTTP
/1.1" ************.5ms
************* [04/Aug/2025:10:23:08 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 33572 1951.7ms
************** [04/Aug/2025:10:23:35 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.1ms
116.178.224.40 [04/Aug/2025:10:24:20 +0800] "GET /api/report/mzlis HTTP/1.1" 200 11494 2223.5ms
************** [04/Aug/2025:10:24:22 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
************** [04/Aug/2025:10:24:35 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1860.4ms
************* [04/Aug/2025:10:24:44 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 18523 1032.4ms
************** [04/Aug/2025:10:24:53 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1387.7ms
************** [04/Aug/2025:10:25:07 +0800] "GET /api/erhcCard/getQrCode?patientId=1241876 HTTP/1.1" 200 468
 1061.8ms
************* [04/Aug/2025:10:25:07 +0800] "GET /api/erhcCard/getQrCode?patientId=863125 HTTP/1.1" 200 422 2
806.2ms
************* [04/Aug/2025:10:25:57 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2101 1968.7ms
*********** [04/Aug/2025:10:26:17 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1262 6638.1ms
************* [04/Aug/2025:10:26:25 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2101 1327.8ms
*********** [04/Aug/2025:10:26:30 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.0ms
************** [04/Aug/2025:10:26:34 +0800] "GET /api/erhcCard/getQrCode?patientId=1144600 HTTP/1.1" 200 422
 1815.8ms
************* [04/Aug/2025:10:26:35 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2101 1480.7ms
************* [04/Aug/2025:10:26:56 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2101 1959.5ms
************** [04/Aug/2025:10:27:17 +0800] "GET /api/erhcCard/getQrCode?patientId=1240922 HTTP/1.1" 200 474
 1291.6ms
************** [04/Aug/2025:10:28:19 +0800] "GET /api/erhcCard/getQrCode?patientId=1242123 HTTP/1.1" 200 468
 1501.4ms
************* [04/Aug/2025:10:28:30 +0800] "GET /api/erhcCard/getQrCode?patientId=1242132 HTTP/1.1" 200 468 
4039.6ms
************* [04/Aug/2025:10:28:30 +0800] "GET /api/erhcCard/getQrCode?patientId=1069774 HTTP/1.1" 200 422 
3183.7ms
************** [04/Aug/2025:10:28:30 +0800] "GET /api/erhcCard/getQrCode?patientId=1181302 HTTP/1.1" 200 422
 3666.9ms
************** [04/Aug/2025:10:28:30 +0800] "GET /api/erhcCard/getQrCode?patientId=1232993 HTTP/1.1" 200 471
 3100.8ms
************* [04/Aug/2025:10:28:30 +0800] "GET /api/erhcCard/getQrCode?patientId=1242129 HTTP/1.1" 200 419 
1519.2ms
************** [04/Aug/2025:10:29:09 +0800] "GET /api/erhcCard/getQrCode?patientId=1241876 HTTP/1.1" 200 468
 4559.8ms
124.88.0.29 [04/Aug/2025:10:29:09 +0800] "GET /api/erhcCard/getQrCode?patientId=1095743 HTTP/1.1" 200 422 22
96.5ms
************* [04/Aug/2025:10:29:09 +0800] "GET /api/erhcCard/getQrCode?patientId=1242112 HTTP/1.1" 200 484 
2248.7ms
************* [04/Aug/2025:10:29:09 +0800] "GET /api/erhcCard/getQrCode?patientId=1242129 HTTP/1.1" 200 419 
1750.1ms
************** [04/Aug/2025:10:29:13 +0800] "GET /api/erhcCard/getQrCode?patientId=1241876 HTTP/1.1" 200 468
 1347.0ms
************** [04/Aug/2025:10:29:21 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
*********** [04/Aug/2025:10:29:41 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1262 7349.2ms
*********** [04/Aug/2025:10:29:46 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.6ms
************* [04/Aug/2025:10:29:50 +0800] "POST /api/erhcCard/bind HTTP/1.1" ************.6ms
************* [04/Aug/2025:10:30:00 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2236 2032.8ms
*************** [04/Aug/2025:10:30:01 +0800] "GET /api/erhcCard/getQrCode?patientId=1241629 HTTP/1.1" 200 46
5 2350.3ms
************** [04/Aug/2025:10:30:21 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 77 1033.2ms
************* [04/Aug/2025:10:30:22 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4817 1016.4ms
************** [04/Aug/2025:10:30:47 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
************* [04/Aug/2025:10:30:52 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2501 1928.4ms
************** [04/Aug/2025:10:31:11 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4164 1043.2ms
************* [04/Aug/2025:10:31:13 +0800] "GET /api/erhcCard/getQrCode?patientId=1069774 HTTP/1.1" 200 422 
1060.0ms
*********** [04/Aug/2025:10:31:16 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1981.3ms
*********** [04/Aug/2025:10:31:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1989.6ms
************* [04/Aug/2025:10:31:36 +0800] "GET /api/erhcCard/getQrCode?patientId=890090 HTTP/1.1" 200 468 2
087.8ms
************* [04/Aug/2025:10:31:41 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 4402 1923.7ms
************** [04/Aug/2025:10:31:41 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2908 1346.1ms
************* [04/Aug/2025:10:31:50 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 4402 1818.6ms
39.144.208.196 [04/Aug/2025:10:31:55 +0800] "GET /api/erhcCard/getQrCode?patientId=1190704 HTTP/1.1" 200 422
 1015.8ms
*************** [04/Aug/2025:10:31:55 +0800] "GET /api/erhcCard/getQrCode?patientId=1241748 HTTP/1.1" 200 46
8 1025.4ms
110.156.88.52 [04/Aug/2025:10:32:00 +0800] "GET /api/erhcCard/getQrCode?patientId=835240 HTTP/1.1" 200 468 1
023.9ms
************* [04/Aug/2025:10:32:07 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 5030 1115.1ms
************* [04/Aug/2025:10:32:38 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 5030 1003.9ms
************** [04/Aug/2025:10:32:45 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 1967.9ms
************** [04/Aug/2025:10:32:48 +0800] "GET /api/erhcCard/getQrCode?patientId=1241876 HTTP/1.1" 200 468
 1385.1ms
*************** [04/Aug/2025:10:32:52 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1273 6039.8ms
*************** [04/Aug/2025:10:32:58 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.9ms
************** [04/Aug/2025:10:33:00 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.7ms
************** [04/Aug/2025:10:33:02 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************** [04/Aug/2025:10:33:31 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
*************** [04/Aug/2025:10:33:34 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1792.7ms
************** [04/Aug/2025:10:34:06 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 1462.6ms
************* [04/Aug/2025:10:34:07 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 2089.4ms
************** [04/Aug/2025:10:34:13 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
************* [04/Aug/2025:10:34:18 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 1825.0ms
*************** [04/Aug/2025:10:34:51 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.8ms
************* [04/Aug/2025:10:35:04 +0800] "GET /api/erhcCard/getQrCode?patientId=1039030 HTTP/1.1" 200 422 
3849.2ms
************** [04/Aug/2025:10:35:04 +0800] "GET /api/erhcCard/getQrCode?patientId=1241396 HTTP/1.1" 200 422
 1867.4ms
110.157.71.227 [04/Aug/2025:10:35:04 +0800] "GET /api/erhcCard/getQrCode?patientId=1100547 HTTP/1.1" 200 471
 5066.9ms
************* [04/Aug/2025:10:35:17 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 5030 1078.6ms
************** [04/Aug/2025:10:35:21 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 1948.0ms
************** [04/Aug/2025:10:36:38 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.7ms
************** [04/Aug/2025:10:36:40 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
*************** [04/Aug/2025:10:37:05 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1267 6307.8ms
************* [04/Aug/2025:10:37:13 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3451 1978.1ms
************* [04/Aug/2025:10:37:23 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3451 1883.2ms
************** [04/Aug/2025:10:37:31 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.2ms
************* [04/Aug/2025:10:37:35 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3451 1932.7ms
*************** [04/Aug/2025:10:37:43 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1273 5184.2ms
*************** [04/Aug/2025:10:37:48 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.8ms
************** [04/Aug/2025:10:38:22 +0800] "GET /api/erhcCard/getQrCode?patientId=1241549 HTTP/1.1" 200 422
 1111.6ms
************** [04/Aug/2025:10:38:49 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
************** [04/Aug/2025:10:38:50 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
************ [04/Aug/2025:10:38:55 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3787 2128.9ms
************ [04/Aug/2025:10:39:05 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3787 1904.0ms
************** [04/Aug/2025:10:39:06 +0800] "GET /api/erhcCard/getQrCode?patientId=1241876 HTTP/1.1" 200 468
 1591.1ms
************* [04/Aug/2025:10:39:06 +0800] "GET /api/erhcCard/getQrCode?patientId=848723 HTTP/1.1" 200 422 1
174.5ms
************* [04/Aug/2025:10:39:06 +0800] "GET /api/erhcCard/getQrCode?patientId=848723 HTTP/1.1" 200 422 1
528.6ms
*************** [04/Aug/2025:10:39:20 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1273 5614.4ms
*************** [04/Aug/2025:10:39:22 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.6ms
************** [04/Aug/2025:10:39:30 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.8ms
************** [04/Aug/2025:10:40:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1075 1854.7ms
************** [04/Aug/2025:10:40:04 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.8ms
********** [04/Aug/2025:10:40:34 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1829.4ms
********** [04/Aug/2025:10:40:38 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1840.3ms
********** [04/Aug/2025:10:40:49 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1994.8ms
********** [04/Aug/2025:10:40:50 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1447.0ms
************** [04/Aug/2025:10:41:36 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
************** [04/Aug/2025:10:41:36 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1894.4ms
*************5 [04/Aug/2025:10:41:40 +0800] "GET /api/erhcCard/getQrCode?patientId=1232994 HTTP/1.1" 200 468
 1457.1ms
************** [04/Aug/2025:10:41:41 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1843.6ms
************** [04/Aug/2025:10:41:49 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1818.8ms
************** [04/Aug/2025:10:41:51 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1575 5823.3ms
************** [04/Aug/2025:10:41:59 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.3ms
************** [04/Aug/2025:10:42:03 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
************* [04/Aug/2025:10:43:11 +0800] "GET /api/erhcCard/getQrCode?patientId=1232025 HTTP/1.1" 200 422 
1804.8ms
************** [04/Aug/2025:10:43:19 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.1ms
************* [04/Aug/2025:10:43:34 +0800] "GET /api/erhcCard/getQrCode?patientId=1085596 HTTP/1.1" 200 422 
2491.0ms
************** [04/Aug/2025:10:43:50 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2664 1059.4ms
************** [04/Aug/2025:10:44:08 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1575 5719.1ms
************** [04/Aug/2025:10:44:24 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.6ms
************** [04/Aug/2025:10:44:40 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2847 1051.3ms
********** [04/Aug/2025:10:45:23 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1851.1ms
*************** [04/Aug/2025:10:45:30 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1225 1021.9ms
************* [04/Aug/2025:10:45:30 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.4ms
************* [04/Aug/2025:10:45:44 +0800] "GET /api/erhcCard/getQrCode?patientId=1242129 HTTP/1.1" 200 419 
2174.6ms
************** [04/Aug/2025:10:45:44 +0800] "GET /api/erhcCard/getQrCode?patientId=1242052 HTTP/1.1" 200 422
 1432.4ms
************** [04/Aug/2025:10:45:44 +0800] "GET /api/erhcCard/getQrCode?patientId=1239935 HTTP/1.1" 200 465
 1685.3ms
************** [04/Aug/2025:10:46:05 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
************** [04/Aug/2025:10:46:12 +0800] "GET /api/report/mzlis HTTP/1.1" ************.0ms
************* [04/Aug/2025:10:46:40 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1237 5107.3ms
************* [04/Aug/2025:10:46:56 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.4ms
************** [04/Aug/2025:10:47:01 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1914.3ms
************* [04/Aug/2025:10:47:05 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1851.7ms
************** [04/Aug/2025:10:47:24 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************** [04/Aug/2025:10:47:53 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1575 6055.7ms
************** [04/Aug/2025:10:47:57 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.0ms
************* [04/Aug/2025:10:48:07 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1253 5098.8ms
************* [04/Aug/2025:10:48:16 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" 200 249 2238.4ms
************** [04/Aug/2025:10:48:31 +0800] "GET /api/report/mzlis HTTP/1.1" 200 767 2018.5ms
************* [04/Aug/2025:10:48:39 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 2140.7ms
************** [04/Aug/2025:10:48:41 +0800] "GET /api/report/mzlis HTTP/1.1" 200 952 1940.6ms
************** [04/Aug/2025:10:48:43 +0800] "GET /api/report/mzlis HTTP/1.1" 200 767 1919.3ms
************** [04/Aug/2025:10:48:53 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 1906.4ms
************** [04/Aug/2025:10:48:58 +0800] "GET /api/report/mzlis HTTP/1.1" 200 767 1431.9ms
************** [04/Aug/2025:10:49:02 +0800] "GET /api/report/mzlis HTTP/1.1" 200 767 1920.2ms
************** [04/Aug/2025:10:49:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 952 1960.5ms
************** [04/Aug/2025:10:49:16 +0800] "GET /api/report/mzlis HTTP/1.1" 200 952 1989.7ms
************** [04/Aug/2025:10:49:16 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************** [04/Aug/2025:10:49:27 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 5424 1059.4ms
************* [04/Aug/2025:10:49:31 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1253 4565.2ms
************* [04/Aug/2025:10:49:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 1921.1ms
************* [04/Aug/2025:10:49:35 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.5ms
************* [04/Aug/2025:10:49:51 +0800] "GET /api/erhcCard/getQrCode?patientId=1242129 HTTP/1.1" 200 419 
2029.2ms
************ [04/Aug/2025:10:49:59 +0800] "GET /api/erhcCard/getQrCode?patientId=1010524 HTTP/1.1" 200 471 2
839.6ms
************** [04/Aug/2025:10:50:11 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 5424 1025.0ms
************** [04/Aug/2025:10:50:19 +0800] "GET /api/report/mzlis HTTP/1.1" ************.1ms
************** [04/Aug/2025:10:50:27 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
************** [04/Aug/2025:10:50:30 +0800] "GET /api/erhcCard/getQrCode?patientId=1241409 HTTP/1.1" 200 421
 1147.3ms
************** [04/Aug/2025:10:50:35 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************* [04/Aug/2025:10:51:03 +0800] "GET /api/erhcCard/getQrCode?patientId=1242132 HTTP/1.1" 200 468 
1624.8ms
************** [04/Aug/2025:10:52:02 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
************** [04/Aug/2025:10:52:04 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
************** [04/Aug/2025:10:52:08 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************** [04/Aug/2025:10:52:21 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
************** [04/Aug/2025:10:52:30 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************* [04/Aug/2025:10:52:53 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1944.9ms
************* [04/Aug/2025:10:53:19 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2887 1059.9ms
110.152.67.130 [04/Aug/2025:10:53:21 +0800] "GET /api/erhcCard/getQrCode?patientId=1203160 HTTP/1.1" 200 422
 4683.6ms
************* [04/Aug/2025:10:53:24 +0800] "GET /api/erhcCard/getQrCode?patientId=1241741 HTTP/1.1" 200 471 
3216.0ms
116.178.172.16 [04/Aug/2025:10:53:24 +0800] "GET /api/erhcCard/getQrCode?patientId=1236487 HTTP/1.1" 200 422
 1050.1ms
116.178.172.16 [04/Aug/2025:10:53:32 +0800] "GET /api/erhcCard/getQrCode?patientId=1236487 HTTP/1.1" 200 422
 1464.3ms
************** [04/Aug/2025:10:53:32 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
106.123.171.75 [04/Aug/2025:10:53:38 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.1ms
************** [04/Aug/2025:10:53:43 +0800] "GET /api/report/mzlis HTTP/1.1" ************.0ms
************* [04/Aug/2025:10:53:59 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 2057.9ms
************* [04/Aug/2025:10:54:01 +0800] "GET /api/erhcCard/getQrCode?patientId=1242138 HTTP/1.1" 200 465 
1233.2ms
************** [04/Aug/2025:10:54:11 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
************** [04/Aug/2025:10:54:14 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1587 11958.0ms
************** [04/Aug/2025:10:54:19 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
************** [04/Aug/2025:10:54:26 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.1ms
************* [04/Aug/2025:10:54:26 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.1ms
************** [04/Aug/2025:10:54:35 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.5ms
************* [04/Aug/2025:10:54:39 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 1422.4ms
************* [04/Aug/2025:10:54:52 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 1989.4ms
************* [04/Aug/2025:10:55:13 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 1908.9ms
************** [04/Aug/2025:10:55:30 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1278 5544.6ms
************* [04/Aug/2025:10:55:31 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 1453.6ms
************* [04/Aug/2025:10:55:37 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 1915.1ms
************* [04/Aug/2025:10:55:39 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 1778.5ms
************* [04/Aug/2025:10:55:44 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1113 1243.0ms
************** [04/Aug/2025:10:55:44 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" 200 249 2186.9ms
************** [04/Aug/2025:10:56:44 +0800] "GET /api/report/mzlis HTTP/1.1" 200 284 1781.2ms
************** [04/Aug/2025:10:56:49 +0800] "GET /api/report/mzlis HTTP/1.1" 200 284 1802.8ms
************** [04/Aug/2025:10:57:10 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1055.3ms
************** [04/Aug/2025:10:58:17 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3193 1959.8ms
************** [04/Aug/2025:10:58:22 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1587 5607.0ms
************** [04/Aug/2025:10:58:29 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" 200 547 1210.4ms
************* [04/Aug/2025:10:58:47 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1426 5578.6ms
************** [04/Aug/2025:10:58:54 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3193 1877.8ms
************* [04/Aug/2025:10:58:59 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2909 4558.4ms
************* [04/Aug/2025:10:59:03 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 2111.2ms
************** [04/Aug/2025:10:59:08 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3193 1447.7ms
************** [04/Aug/2025:10:59:12 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1361.2ms
************** [04/Aug/2025:10:59:16 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1428.7ms
************* [04/Aug/2025:10:59:17 +0800] "GET /api/report/mzlis HTTP/1.1" 200 279 1816.0ms
************** [04/Aug/2025:10:59:31 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3193 1955.6ms
************** [04/Aug/2025:11:00:00 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 4088.8ms
************* [04/Aug/2025:11:00:14 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3451 1999.0ms
*********** [04/Aug/2025:11:00:21 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 324 1057.0ms
************* [04/Aug/2025:11:00:22 +0800] "GET /api/report/mzlis HTTP/1.1" 200 279 1916.6ms
************* [04/Aug/2025:11:00:26 +0800] "GET /api/report/mzlis HTTP/1.1" 200 279 1290.9ms
39.144.197.43 [04/Aug/2025:11:00:48 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1572 1068.5ms
49.112.227.250 [04/Aug/2025:11:00:48 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1444 1099.4ms
************ [04/Aug/2025:11:00:54 +0800] "GET /api/report/mzlis HTTP/1.1" 200 808 1785.3ms
************ [04/Aug/2025:11:01:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 808 1764.1ms
49.112.227.250 [04/Aug/2025:11:01:53 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1444 1047.2ms
************** [04/Aug/2025:11:02:17 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 512 1030.3ms
************** [04/Aug/2025:11:02:34 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 532 1097.8ms
************* [04/Aug/2025:11:02:36 +0800] "GET /api/report/mzlis HTTP/1.1" 200 280 1973.8ms
110.152.201.47 [04/Aug/2025:11:02:48 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1368.2ms
************* [04/Aug/2025:11:02:51 +0800] "GET /api/report/mzlis HTTP/1.1" 200 280 1845.9ms
************* [04/Aug/2025:11:02:51 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 1297.8ms
110.152.201.47 [04/Aug/2025:11:03:01 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1848.6ms
110.152.201.47 [04/Aug/2025:11:03:08 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1418.9ms
************** [04/Aug/2025:11:03:11 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3058 1070.5ms
39.144.196.2 [04/Aug/2025:11:03:18 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2501 1449.6ms
************** [04/Aug/2025:11:03:24 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1225 1020.6ms
************** [04/Aug/2025:11:03:35 +0800] "GET /api/report/mzlis HTTP/1.1" 200 952 1940.0ms
************** [04/Aug/2025:11:03:37 +0800] "GET /api/report/mzlis HTTP/1.1" 200 952 1234.1ms
49.112.227.250 [04/Aug/2025:11:04:04 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1444 1056.8ms
************** [04/Aug/2025:11:04:05 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3058 1137.2ms
************** [04/Aug/2025:11:04:10 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3058 1043.4ms
************** [04/Aug/2025:11:04:29 +0800] "GET /api/report/mzlis HTTP/1.1" 200 952 1854.8ms
************* [04/Aug/2025:11:04:35 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2863 1948.8ms
************** [04/Aug/2025:11:04:42 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 542 1035.4ms
*************** [04/Aug/2025:11:04:43 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2971 1850.8ms
************** [04/Aug/2025:11:04:51 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1909.3ms
************** [04/Aug/2025:11:05:27 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.6ms
************* [04/Aug/2025:11:05:31 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************ [04/Aug/2025:11:05:59 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
************** [04/Aug/2025:11:06:09 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.5ms
************* [04/Aug/2025:11:06:11 +0800] "GET /api/erhcCard/getQrCode?patientId=1242183 HTTP/1.1" 200 422 
1236.7ms
************* [04/Aug/2025:11:06:14 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1426 5973.6ms
************* [04/Aug/2025:11:06:21 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6427 1441.2ms
************* [04/Aug/2025:11:06:22 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.0ms
************* [04/Aug/2025:11:06:25 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6427 1237.8ms
************** [04/Aug/2025:11:06:38 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 2005.4ms
************** [04/Aug/2025:11:06:44 +0800] "GET /api/erhcCard/getQrCode?patientId=1241725 HTTP/1.1" 200 422
 1121.6ms
************** [04/Aug/2025:11:06:46 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
************** [04/Aug/2025:11:07:08 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 1797.4ms
************** [04/Aug/2025:11:07:15 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
************ [04/Aug/2025:11:07:38 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1226 1879.6ms
************** [04/Aug/2025:11:07:45 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 1475.0ms
************ [04/Aug/2025:11:07:53 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************** [04/Aug/2025:11:07:55 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 1518.1ms
************** [04/Aug/2025:11:07:59 +0800] "GET /api/report/mzlis HTTP/1.1" ************.0ms
************** [04/Aug/2025:11:08:03 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 1487.7ms
************** [04/Aug/2025:11:08:11 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.7ms
************** [04/Aug/2025:11:08:12 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1857.0ms
********** [04/Aug/2025:11:08:32 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1978.5ms
110.157.104.220 [04/Aug/2025:11:08:35 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.0ms
********** [04/Aug/2025:11:08:35 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1266.8ms
************** [04/Aug/2025:11:08:36 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1750.8ms
************* [04/Aug/2025:11:08:39 +0800] "GET /api/erhcCard/getQrCode?patientId=1241859 HTTP/1.1" 200 509 
1894.6ms
************** [04/Aug/2025:11:08:47 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
************** [04/Aug/2025:11:09:10 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************** [04/Aug/2025:11:09:14 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
116.178.222.165 [04/Aug/2025:11:09:16 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" ************.5ms
116.178.222.165 [04/Aug/2025:11:09:25 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" ************.5ms
************** [04/Aug/2025:11:09:27 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
********** [04/Aug/2025:11:09:35 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2107 1813.4ms
39.144.196.31 [04/Aug/2025:11:09:39 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 5237 1337.2ms
39.144.196.114 [04/Aug/2025:11:09:54 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.8ms
39.144.196.31 [04/Aug/2025:11:09:54 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 5237 1441.8ms
************** [04/Aug/2025:11:09:56 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
************** [04/Aug/2025:11:09:59 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
39.144.196.114 [04/Aug/2025:11:10:01 +0800] "GET /open/schedule/doctors?departmentCode=101&date=2025-08-04 H
TTP/1.1" 200 10908 2281.4ms
39.144.196.114 [04/Aug/2025:11:10:01 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=101&date=20
25-08-04&days=7 HTTP/1.1" 200 66543 2209.5ms
************** [04/Aug/2025:11:10:01 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
************** [04/Aug/2025:11:10:04 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************** [04/Aug/2025:11:10:07 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************** [04/Aug/2025:11:10:11 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************** [04/Aug/2025:11:10:18 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
116.178.224.26 [04/Aug/2025:11:10:35 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 7969 1074.9ms
36.108.117.24 [04/Aug/2025:11:10:57 +0800] "GET /api/erhcCard/getQrCode?patientId=1032537 HTTP/1.1" 200 422 
1812.3ms
************ [04/Aug/2025:11:11:57 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1226 1831.7ms
************ [04/Aug/2025:11:12:12 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1226 1842.0ms
************** [04/Aug/2025:11:12:19 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1917.6ms
49.112.224.139 [04/Aug/2025:11:12:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 1417.3ms
************ [04/Aug/2025:11:12:29 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1226 1973.0ms
************** [04/Aug/2025:11:12:40 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 4083.5ms
49.112.224.139 [04/Aug/2025:11:12:43 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 1899.6ms
49.118.205.54 [04/Aug/2025:11:12:44 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4093 1604.6ms
49.112.224.139 [04/Aug/2025:11:13:03 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 1844.5ms
49.112.224.139 [04/Aug/2025:11:13:14 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 1443.3ms
************** [04/Aug/2025:11:13:15 +0800] "POST /api/reservation HTTP/1.1" ************.3ms
39.144.196.20 [04/Aug/2025:11:13:22 +0800] "GET /api/erhcCard/getQrCode?patientId=1043461 HTTP/1.1" 200 471 
1552.6ms
49.112.224.139 [04/Aug/2025:11:13:29 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 2103.9ms
49.112.224.139 [04/Aug/2025:11:13:37 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 1501.2ms
49.112.224.139 [04/Aug/2025:11:13:42 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 1375.9ms
39.144.196.21 [04/Aug/2025:11:13:52 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1534 1440.0ms
39.144.196.10 [04/Aug/2025:11:13:56 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" 200 47 1398.6ms
39.144.197.15 [04/Aug/2025:11:14:09 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1937.4ms
39.144.197.15 [04/Aug/2025:11:14:14 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1329.9ms
*************2 [04/Aug/2025:11:14:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2006.1ms
39.144.197.15 [04/Aug/2025:11:14:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1905.4ms
************** [04/Aug/2025:11:14:38 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
39.144.196.4 [04/Aug/2025:11:14:39 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 235898 2018.9ms
************** [04/Aug/2025:11:14:51 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
************* [04/Aug/2025:11:15:32 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2057.9ms
************* [04/Aug/2025:11:15:34 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2045.1ms
36.108.110.22 [04/Aug/2025:11:15:36 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************** [04/Aug/2025:11:15:40 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 1561.7ms
39.144.196.42 [04/Aug/2025:11:15:41 +0800] "GET /api/erhcCard/getQrCode?patientId=1241998 HTTP/1.1" 200 422 
2806.2ms
49.112.224.139 [04/Aug/2025:11:16:12 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1251 2004.3ms
49.112.224.5 [04/Aug/2025:11:16:14 +0800] "GET /api/erhcCard/getQrCode?patientId=1190057 HTTP/1.1" 200 474 1
649.8ms
39.144.197.21 [04/Aug/2025:11:16:20 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
36.108.110.22 [04/Aug/2025:11:16:26 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
39.144.197.21 [04/Aug/2025:11:16:27 +0800] "GET /api/report/mzlis HTTP/1.1" ************.0ms
************** [04/Aug/2025:11:16:31 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
************** [04/Aug/2025:11:16:51 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1955.7ms
************** [04/Aug/2025:11:16:55 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1194 1888.7ms
************** [04/Aug/2025:11:16:57 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.8ms
116.178.122.35 [04/Aug/2025:11:17:08 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 3226 1876.9ms
116.178.232.53 [04/Aug/2025:11:17:42 +0800] "POST /api/erhcCard/bind HTTP/1.1" ************.6ms
140.207.54.76 [04/Aug/2025:11:18:17 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.7ms
************** [04/Aug/2025:11:18:24 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 1870.6ms
************** [04/Aug/2025:11:18:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1194 1776.8ms
116.178.172.1 [04/Aug/2025:11:18:50 +0800] "GET /open/schedule/doctors?departmentCode=103&date=2025-08-04 HT
TP/1.1" 200 6412 1016.5ms
39.144.196.20 [04/Aug/2025:11:18:51 +0800] "GET /api/erhcCard/getQrCode?patientId=1043461 HTTP/1.1" 200 471 
1951.6ms
43.242.153.7 [04/Aug/2025:11:18:51 +0800] "GET /api/erhcCard/getQrCode?patientId=1242204 HTTP/1.1" 200 422 1
822.9ms
110.152.201.47 [04/Aug/2025:11:18:58 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 2038.9ms
39.144.196.2 [04/Aug/2025:11:19:03 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2501 2041.0ms
110.152.201.47 [04/Aug/2025:11:19:07 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1472.3ms
43.242.153.39 [04/Aug/2025:11:19:09 +0800] "GET /api/erhcCard/getQrCode?patientId=1223784 HTTP/1.1" 200 422 
1203.2ms
************** [04/Aug/2025:11:19:15 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1078.1ms
************** [04/Aug/2025:11:19:27 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2007.2ms
************** [04/Aug/2025:11:19:41 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1903.2ms
************* [04/Aug/2025:11:19:42 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" ************.2ms
49.116.128.79 [04/Aug/2025:11:19:42 +0800] "GET /api/erhcCard/getQrCode?patientId=999819 HTTP/1.1" 200 422 1
037.6ms
140.207.54.77 [04/Aug/2025:11:19:44 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.6ms
************** [04/Aug/2025:11:19:49 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1949.0ms
************** [04/Aug/2025:11:19:57 +0800] "GET /api/erhcCard/getQrCode?patientId=1121886 HTTP/1.1" 200 474
 1664.4ms
************** [04/Aug/2025:11:20:03 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1089.6ms
140.207.54.77 [04/Aug/2025:11:20:12 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
************* [04/Aug/2025:11:20:13 +0800] "POST /api/zhenjian/checkBalanceAndCreateWeixinRechargeOrder HTTP
/1.1" ************.7ms
39.144.197.20 [04/Aug/2025:11:20:15 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3170 1515.9ms
116.178.173.212 [04/Aug/2025:11:20:17 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
************** [04/Aug/2025:11:20:22 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1072.8ms
************* [04/Aug/2025:11:20:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1826 2006.2ms
140.207.54.77 [04/Aug/2025:11:20:34 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
************* [04/Aug/2025:11:20:36 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1826 1448.7ms
116.178.173.212 [04/Aug/2025:11:20:37 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
39.144.197.20 [04/Aug/2025:11:20:38 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3170 1963.8ms
************* [04/Aug/2025:11:20:42 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1826 2052.2ms
43.242.153.39 [04/Aug/2025:11:20:52 +0800] "GET /api/zhenjian/pendingSettlements HTTP/1.1" 200 62 3347.4ms
116.178.172.1 [04/Aug/2025:11:20:52 +0800] "GET /open/schedule/tickets?doctorCode=586&date=2025-08-04&ampm=1
 HTTP/1.1" 200 47 3128.9ms
39.144.196.62 [04/Aug/2025:11:20:52 +0800] "POST /api/patient/switchZhuyuanNoV2 HTTP/1.1" 200 89 1393.0ms
110.155.205.16 [04/Aug/2025:11:20:52 +0800] "GET /api/zhenjian/pendingSettlements HTTP/1.1" ************.1ms
110.154.64.204 [04/Aug/2025:11:20:52 +0800] "GET /api/zhenjian/pendingSettlements HTTP/1.1" 200 3042 1727.1m
s
110.154.64.204 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1242107&id=3655 HTTP/
1.1" 200 56 1884.1ms
116.178.172.1 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1242202&id=3768 HTTP/1
.1" 200 54 3197.9ms
43.242.153.39 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1223784&id=1082 HTTP/1
.1" 200 54 3588.8ms
************* [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1223784&id=3772 HTTP/1
.1" 200 54 5051.1ms
43.242.153.39 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1223784&id=1082 HTTP/1
.1" 200 54 3613.4ms
************* [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1241927&id=3576 HTTP/1
.1" 200 56 3228.1ms
39.144.196.11 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1242150&id=3698 HTTP/1
.1" 200 54 3436.6ms
************** [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1241631&id=2982 HTTP/
1.1" 200 54 4523.1ms
110.155.205.16 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1239305&id=3678 HTTP/
1.1" 200 56 2526.4ms
116.178.173.212 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1145303&id=3750 HTTP
/1.1" 200 54 3312.5ms
49.116.139.31 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=938842&id=1701 HTTP/1.
1" 200 54 3581.3ms
39.144.209.205 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1242200&id=3765 HTTP/
1.1" 200 54 2707.4ms
************* [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1241925&id=3581 HTTP/1
.1" 200 56 3298.6ms
110.155.205.16 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1239305&id=3678 HTTP/
1.1" 200 56 2593.1ms
110.154.64.204 [04/Aug/2025:11:20:53 +0800] "GET /api/patient/menzhenBalance?patientId=1242107&id=3655 HTTP/
1.1" 200 56 1964.8ms
124.88.48.150 [04/Aug/2025:11:20:53 +0800] "POST /api/erhcCard/bind HTTP/1.1" ************.8ms
************** [04/Aug/2025:11:20:54 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 6108.5ms
116.178.173.212 [04/Aug/2025:11:20:54 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
43.242.153.7 [04/Aug/2025:11:21:18 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=101&date=2025
-08-04&days=7 HTTP/1.1" 200 66543 3967.1ms
************* [04/Aug/2025:11:21:23 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2971 2000.8ms
140.207.54.73 [04/Aug/2025:11:21:36 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.2ms
140.207.54.77 [04/Aug/2025:11:21:47 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
************ [04/Aug/2025:11:22:10 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 22021 1045.1ms
39.144.197.12 [04/Aug/2025:11:22:20 +0800] "POST /api/reservation HTTP/1.1" ************.0ms
************* [04/Aug/2025:11:22:23 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1826 2032.6ms
218.31.206.63 [04/Aug/2025:11:22:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1951.7ms
218.31.206.63 [04/Aug/2025:11:22:30 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1961.6ms
110.152.201.47 [04/Aug/2025:11:22:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1412.0ms
218.31.206.63 [04/Aug/2025:11:22:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1959.8ms
110.152.201.47 [04/Aug/2025:11:22:38 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 2011.9ms
110.152.201.47 [04/Aug/2025:11:22:53 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 23070 1079.8ms
************* [04/Aug/2025:11:23:00 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1826 2117.6ms
140.207.54.75 [04/Aug/2025:11:23:37 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.7ms
************ [04/Aug/2025:11:23:45 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************* [04/Aug/2025:11:24:10 +0800] "GET /api/erhcCard/getQrCode?patientId=1240722 HTTP/1.1" 200 422 
1421.6ms
39.144.197.20 [04/Aug/2025:11:24:14 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3170 1987.6ms
************ [04/Aug/2025:11:24:18 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
39.144.197.20 [04/Aug/2025:11:24:25 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3170 1980.1ms
************ [04/Aug/2025:11:24:26 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
************* [04/Aug/2025:11:24:28 +0800] "GET /open/schedule/doctors?departmentCode=120&date=2025-08-04 HT
TP/1.1" 200 1854 1034.8ms
************* [04/Aug/2025:11:24:30 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.4ms
************ [04/Aug/2025:11:24:36 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
************** [04/Aug/2025:11:24:55 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 77 11547.4ms
************ [04/Aug/2025:11:25:00 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 1437.8ms
************** [04/Aug/2025:11:25:09 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1428 1067.1ms
************ [04/Aug/2025:11:25:18 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 1953.2ms
************ [04/Aug/2025:11:25:20 +0800] "POST /api/erhcCard/bind HTTP/1.1" ************.3ms
************ [04/Aug/2025:11:25:27 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1463.2ms
************ [04/Aug/2025:11:25:42 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************* [04/Aug/2025:11:26:00 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.8ms
************** [04/Aug/2025:11:26:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1996.4ms
************** [04/Aug/2025:11:26:04 +0800] "GET /api/erhcCard/getQrCode?patientId=1171436 HTTP/1.1" 200 422
 1132.2ms
************* [04/Aug/2025:11:26:10 +0800] "GET /api/report/mzlis HTTP/1.1" 200 12297 1648.4ms
************** [04/Aug/2025:11:26:26 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2238 1126.7ms
************** [04/Aug/2025:11:26:39 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1428 1036.1ms
************** [04/Aug/2025:11:26:41 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
140.207.54.76 [04/Aug/2025:11:26:58 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
49.112.226.151 [04/Aug/2025:11:26:59 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.8ms
49.112.226.151 [04/Aug/2025:11:27:10 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.9ms
49.112.226.151 [04/Aug/2025:11:27:22 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.9ms
************** [04/Aug/2025:11:27:29 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1987.2ms
************** [04/Aug/2025:11:27:34 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2009.6ms
49.112.226.151 [04/Aug/2025:11:27:39 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 296 1159.3ms
************** [04/Aug/2025:11:27:44 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3271 1028.9ms
110.152.254.89 [04/Aug/2025:11:27:58 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1943.2ms
************** [04/Aug/2025:11:28:03 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1917.7ms
39.144.32.9 [04/Aug/2025:11:28:06 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1349.5ms
************** [04/Aug/2025:11:28:07 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1344.3ms
39.144.209.205 [04/Aug/2025:11:28:20 +0800] "GET /api/erhcCard/getQrCode?patientId=1242200 HTTP/1.1" 200 465
 1394.0ms
39.144.197.20 [04/Aug/2025:11:28:20 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3170 1938.5ms
140.207.54.77 [04/Aug/2025:11:28:50 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
140.207.54.73 [04/Aug/2025:11:29:05 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
110.152.254.89 [04/Aug/2025:11:29:09 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1816.8ms
110.152.254.89 [04/Aug/2025:11:29:15 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1900.7ms
************ [04/Aug/2025:11:30:02 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1910.8ms
************** [04/Aug/2025:11:30:04 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1030.3ms
************ [04/Aug/2025:11:30:12 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1771.7ms
140.207.54.76 [04/Aug/2025:11:30:14 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.0ms
39.144.197.31 [04/Aug/2025:11:30:22 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2375 1902.6ms
************** [04/Aug/2025:11:30:37 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1044.0ms
************ [04/Aug/2025:11:30:41 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2028.4ms
************ [04/Aug/2025:11:30:59 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1961.9ms
************ [04/Aug/2025:11:31:02 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2033.1ms
************** [04/Aug/2025:11:31:14 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1119.7ms
************ [04/Aug/2025:11:31:15 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1404.4ms
************ [04/Aug/2025:11:31:18 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1419.5ms
************** [04/Aug/2025:11:31:20 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1322.0ms
************ [04/Aug/2025:11:31:31 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 996 1070.1ms
************ [04/Aug/2025:11:31:47 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1782.3ms
39.144.196.31 [04/Aug/2025:11:31:57 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 5237 1408.0ms
39.144.196.31 [04/Aug/2025:11:32:00 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 5237 1853.3ms
************ [04/Aug/2025:11:32:08 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1943.3ms
110.156.89.147 [04/Aug/2025:11:32:13 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2746 1012.8ms
************** [04/Aug/2025:11:32:20 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 1590.2ms
110.156.89.147 [04/Aug/2025:11:32:28 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2746 1011.7ms
116.178.120.5 [04/Aug/2025:11:33:02 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1523 2066.8ms
218.31.206.63 [04/Aug/2025:11:33:07 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1969.6ms
218.31.206.63 [04/Aug/2025:11:33:10 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1852.4ms
110.152.201.47 [04/Aug/2025:11:33:11 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1830.4ms
218.31.206.63 [04/Aug/2025:11:33:13 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1903.2ms
************** [04/Aug/2025:11:33:14 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1050.9ms
116.178.120.5 [04/Aug/2025:11:33:14 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1523 1270.9ms
110.152.201.47 [04/Aug/2025:11:33:15 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1222.3ms
116.178.120.5 [04/Aug/2025:11:33:23 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1523 1375.3ms
116.178.120.5 [04/Aug/2025:11:33:25 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1523 1304.2ms
116.178.120.234 [04/Aug/2025:11:33:33 +0800] "GET /api/erhcCard/getQrCode?patientId=1242203 HTTP/1.1" 200 47
1 2338.0ms
39.144.197.20 [04/Aug/2025:11:33:45 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3412 2062.1ms
49.112.224.5 [04/Aug/2025:11:33:55 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 10446 1068.4ms
124.88.48.150 [04/Aug/2025:11:34:00 +0800] "GET /api/erhcCard/getQrCode?patientId=855097 HTTP/1.1" 200 419 2
362.6ms
************** [04/Aug/2025:11:34:12 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1055.6ms
************** [04/Aug/2025:11:34:18 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.6ms
222.82.248.154 [04/Aug/2025:11:34:21 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" ************.6ms
39.144.197.20 [04/Aug/2025:11:34:22 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3412 1967.0ms
************** [04/Aug/2025:11:34:32 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 1871.1ms
************** [04/Aug/2025:11:34:32 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1911.9ms
************** [04/Aug/2025:11:34:33 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 1845.8ms
************** [04/Aug/2025:11:34:49 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1740.7ms
************** [04/Aug/2025:11:34:50 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1126.7ms
************** [04/Aug/2025:11:34:53 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1914.3ms
************** [04/Aug/2025:11:35:00 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1240.9ms
110.157.89.79 [04/Aug/2025:11:35:00 +0800] "GET /api/erhcCard/getQrCode?patientId=1240387 HTTP/1.1" 200 422 
1439.2ms
************** [04/Aug/2025:11:35:03 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 6358 1455.0ms
39.144.208.196 [04/Aug/2025:11:35:18 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1345.2ms
************** [04/Aug/2025:11:35:20 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.8ms
************* [04/Aug/2025:11:35:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 1533.9ms
************** [04/Aug/2025:11:35:58 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************** [04/Aug/2025:11:36:15 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1110.9ms
************* [04/Aug/2025:11:36:21 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 2028.2ms
************** [04/Aug/2025:11:36:27 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.0ms
39.144.197.31 [04/Aug/2025:11:36:31 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2375 2117.9ms
39.144.196.10 [04/Aug/2025:11:37:12 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 6869 1057.7ms
************ [04/Aug/2025:11:37:15 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1906.2ms
************ [04/Aug/2025:11:37:25 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1713 2075.5ms
************ [04/Aug/2025:11:37:29 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2025.0ms
************* [04/Aug/2025:11:37:31 +0800] "GET /api/erhcCard/getQrCode?patientId=1242132 HTTP/1.1" 200 468 
1900.2ms
************ [04/Aug/2025:11:37:37 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************ [04/Aug/2025:11:37:37 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1713 1914.7ms
************ [04/Aug/2025:11:37:43 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************* [04/Aug/2025:11:37:49 +0800] "GET /api/erhcCard/getQrCode?patientId=931413 HTTP/1.1" 200 465 2
070.3ms
************** [04/Aug/2025:11:37:55 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1246 5063.5ms
************** [04/Aug/2025:11:37:56 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=103&date=20
25-08-04&days=7 HTTP/1.1" 200 35157 1163.3ms
************** [04/Aug/2025:11:37:56 +0800] "GET /open/schedule/doctors?departmentCode=103&date=2025-08-04 H
TTP/1.1" 200 6412 1235.8ms
************** [04/Aug/2025:11:37:59 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.5ms
************** [04/Aug/2025:11:38:04 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.3ms
************* [04/Aug/2025:11:38:09 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 1520.1ms
************* [04/Aug/2025:11:38:12 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 1484.5ms
************* [04/Aug/2025:11:38:26 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 9399 1526.7ms
************* [04/Aug/2025:11:38:30 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2045 1095.7ms
************* [04/Aug/2025:11:38:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 1916.8ms
************* [04/Aug/2025:11:38:35 +0800] "GET /api/report/mzlis HTTP/1.1" 200 747 1839.0ms
************* [04/Aug/2025:11:38:43 +0800] "GET /api/report/mzlis HTTP/1.1" 200 747 1902.8ms
************** [04/Aug/2025:11:38:45 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.2ms
************* [04/Aug/2025:11:38:46 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 2004.3ms
************** [04/Aug/2025:11:38:49 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.6ms
************ [04/Aug/2025:11:38:53 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
************* [04/Aug/2025:11:38:53 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 1442.8ms
116.178.173.158 [04/Aug/2025:11:38:58 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.4m
s
************** [04/Aug/2025:11:38:58 +0800] "GET /api/erhcCard/getQrCode?patientId=1121886 HTTP/1.1" 200 474
 1355.2ms
************** [04/Aug/2025:11:39:09 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1043.4ms
************* [04/Aug/2025:11:39:10 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4694 1524.2ms
************** [04/Aug/2025:11:39:14 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 1603.0ms
************** [04/Aug/2025:11:39:18 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1261 6236.4ms
********** [04/Aug/2025:11:39:19 +0800] "GET /api/erhcCard/getQrCode?patientId=1241395 HTTP/1.1" 200 422 175
6.4ms
************** [04/Aug/2025:11:39:21 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.9ms
************** [04/Aug/2025:11:39:22 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.7ms
************** [04/Aug/2025:11:39:30 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1989.8ms
************** [04/Aug/2025:11:39:41 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1260 5521.6ms
************** [04/Aug/2025:11:39:44 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
************** [04/Aug/2025:11:39:46 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" ************.0ms
************** [04/Aug/2025:11:40:01 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1006.2ms
************** [04/Aug/2025:11:40:16 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4989 1012.2ms
************** [04/Aug/2025:11:40:25 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
************** [04/Aug/2025:11:40:29 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2664 1032.9ms
************ [04/Aug/2025:11:40:36 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1773.4ms
************** [04/Aug/2025:11:40:45 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1983.3ms
************** [04/Aug/2025:11:41:10 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 11714 1102.7ms
************* [04/Aug/2025:11:41:11 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4694 1827.2ms
************* [04/Aug/2025:11:41:12 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 747 1072.9ms
************* [04/Aug/2025:11:41:51 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2441 1034.3ms
************* [04/Aug/2025:11:41:51 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4694 1833.4ms
110.152.201.47 [04/Aug/2025:11:41:51 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1923.4ms
110.152.201.47 [04/Aug/2025:11:41:57 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1419.8ms
*************** [04/Aug/2025:11:42:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1242 1851.8ms
************* [04/Aug/2025:11:42:11 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4694 1477.6ms
************* [04/Aug/2025:11:42:22 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4694 1497.2ms
************* [04/Aug/2025:11:42:34 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4694 1949.7ms
************* [04/Aug/2025:11:42:42 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4694 2001.9ms
************** [04/Aug/2025:11:42:56 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 77 1172.3ms
************** [04/Aug/2025:11:43:22 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 1822.0ms
************* [04/Aug/2025:11:43:40 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2045 1022.9ms
************** [04/Aug/2025:11:43:43 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 1729.0ms
************** [04/Aug/2025:11:44:32 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.9ms
************* [04/Aug/2025:11:44:53 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4144 1073.8ms
************** [04/Aug/2025:11:45:02 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.2ms
************** [04/Aug/2025:11:45:10 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2532 2103.8ms
************* [04/Aug/2025:11:45:25 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4144 1091.8ms
************* [04/Aug/2025:11:45:36 +0800] "GET /api/erhcCard/getQrCode?patientId=1051684 HTTP/1.1" 200 468 
1282.0ms
************ [04/Aug/2025:11:45:41 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 2132.4ms
49.118.199.117 [04/Aug/2025:11:45:42 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.2ms
************ [04/Aug/2025:11:45:44 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1937.2ms
218.31.206.63 [04/Aug/2025:11:45:56 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 2066.6ms
************** [04/Aug/2025:11:45:59 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1227 1088.7ms
************ [04/Aug/2025:11:46:01 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1822.3ms
116.178.173.212 [04/Aug/2025:11:46:26 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
116.178.173.212 [04/Aug/2025:11:46:40 +0800] "GET /api/report/mzlis HTTP/1.1" ************.0ms
116.178.173.212 [04/Aug/2025:11:46:45 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************* [04/Aug/2025:11:46:51 +0800] "GET /api/erhcCard/getQrCode?patientId=1051684 HTTP/1.1" 200 468 
1373.6ms
************** [04/Aug/2025:11:46:52 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************** [04/Aug/2025:11:46:52 +0800] "GET /api/report/mzlis HTTP/1.1" ************.1ms
************ [04/Aug/2025:11:46:55 +0800] "POST /api/patient/switchToJzCard HTTP/1.1" ************.2ms
************* [04/Aug/2025:11:47:02 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1262.8ms
112.43.109.206 [04/Aug/2025:11:47:03 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 ***********.8ms
************* [04/Aug/2025:11:47:08 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1340.4ms
************* [04/Aug/2025:11:47:17 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2471 1845.2ms
************* [04/Aug/2025:11:47:27 +0800] "GET /api/erhcCard/getQrCode?patientId=933221 HTTP/1.1" 200 468 2
531.8ms
39.144.208.232 [04/Aug/2025:11:47:29 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
39.144.196.10 [04/Aug/2025:11:47:31 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 7080 1033.4ms
39.144.208.232 [04/Aug/2025:11:47:33 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
************** [04/Aug/2025:11:47:36 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
************** [04/Aug/2025:11:47:39 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.0ms
39.144.208.232 [04/Aug/2025:11:47:46 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************* [04/Aug/2025:11:47:46 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1267.0ms
112.43.109.206 [04/Aug/2025:11:48:01 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 3561 1298.9ms
************ [04/Aug/2025:11:48:37 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1462.3ms
************* [04/Aug/2025:11:48:37 +0800] "POST /api/menzhen/wxrefund HTTP/1.1" ************.0ms
39.144.197.182 [04/Aug/2025:11:48:46 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2005 1081.8ms
49.112.225.69 [04/Aug/2025:11:49:19 +0800] "GET /api/erhcCard/getQrCode?patientId=1241630 HTTP/1.1" 200 422 
2013.5ms
112.43.4.220 [04/Aug/2025:11:49:50 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2441 1076.6ms
************ [04/Aug/2025:11:49:56 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1902.7ms
110.152.201.47 [04/Aug/2025:11:50:05 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1371.8ms
39.144.196.10 [04/Aug/2025:11:50:25 +0800] "GET /api/report/mzlis HTTP/1.1" 200 9856 1414.4ms
112.43.4.220 [04/Aug/2025:11:50:25 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
112.43.4.220 [04/Aug/2025:11:50:29 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
39.144.196.3 [04/Aug/2025:11:50:31 +0800] "GET /api/erhcCard/getQrCode?patientId=1226498 HTTP/1.1" 200 422 2
002.7ms
39.144.197.12 [04/Aug/2025:11:50:31 +0800] "GET /api/erhcCard/getQrCode?patientId=1241107 HTTP/1.1" 200 474 
2219.9ms
49.112.224.228 [04/Aug/2025:11:50:39 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1481.2ms
39.144.196.10 [04/Aug/2025:11:50:55 +0800] "GET /api/report/mzlis HTTP/1.1" 200 9856 2234.0ms
49.112.224.228 [04/Aug/2025:11:50:55 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1999.8ms
************** [04/Aug/2025:11:50:59 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=103&date=20
25-08-04&days=7 HTTP/1.1" 200 35157 1230.9ms
************** [04/Aug/2025:11:50:59 +0800] "GET /open/schedule/doctors?departmentCode=103&date=2025-08-04 H
TTP/1.1" 200 6412 1293.9ms
49.112.224.228 [04/Aug/2025:11:51:07 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1980.9ms
49.112.224.228 [04/Aug/2025:11:51:13 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1894.9ms
49.112.224.228 [04/Aug/2025:11:51:27 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1326.8ms
************** [04/Aug/2025:11:51:35 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.7ms
49.112.224.228 [04/Aug/2025:11:51:43 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1898.7ms
49.112.224.228 [04/Aug/2025:11:52:03 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1437.3ms
49.112.224.228 [04/Aug/2025:11:52:54 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 1600 1841.3ms
************ [04/Aug/2025:11:52:58 +0800] "GET /api/report/mzlis HTTP/1.1" 200 4230 2103.5ms
************ [04/Aug/2025:11:52:59 +0800] "GET /api/report/mzlis HTTP/1.1" 200 293 1992.5ms
************ [04/Aug/2025:11:53:07 +0800] "GET /api/report/mzlis HTTP/1.1" 200 293 1894.8ms
************ [04/Aug/2025:11:53:14 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 6252 3715.4ms
************* [04/Aug/2025:11:53:30 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1768.1ms
************* [04/Aug/2025:11:53:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1284.7ms
************* [04/Aug/2025:11:53:36 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1954.6ms
39.144.197.55 [04/Aug/2025:11:53:49 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2754 1348.0ms
49.112.224.139 [04/Aug/2025:11:53:50 +0800] "POST /api/menzhen/wxrefund HTTP/1.1" 200 112 1734.6ms
39.144.197.101 [04/Aug/2025:11:54:34 +0800] "POST /api/erhcCard/bind HTTP/1.1" 200 809 1146.1ms
124.88.0.30 [04/Aug/2025:11:55:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 279 1840.3ms
112.43.4.220 [04/Aug/2025:11:55:20 +0800] "GET /api/report/mzlis HTTP/1.1" 200 279 1930.8ms
124.88.0.30 [04/Aug/2025:11:55:32 +0800] "GET /api/report/mzlis HTTP/1.1" 200 279 1932.7ms
************** [04/Aug/2025:11:55:33 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 2083.9ms
************** [04/Aug/2025:11:55:36 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46338 2025.3ms
116.178.172.1 [04/Aug/2025:11:55:42 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1912.9ms
110.152.201.47 [04/Aug/2025:11:55:48 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1428.8ms
110.157.71.244 [04/Aug/2025:11:55:59 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3533 3822.8ms
116.178.173.212 [04/Aug/2025:11:56:01 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************** [04/Aug/2025:11:56:05 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.6ms
124.88.0.30 [04/Aug/2025:11:56:14 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************** [04/Aug/2025:11:56:14 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.3ms
116.178.172.1 [04/Aug/2025:11:56:28 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1026 1046.5ms
39.144.196.42 [04/Aug/2025:11:56:33 +0800] "GET /api/erhcCard/getQrCode?patientId=1241998 HTTP/1.1" 200 422 
2025.0ms
116.178.173.212 [04/Aug/2025:11:56:39 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
110.157.71.244 [04/Aug/2025:11:56:56 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2097.4ms
124.88.0.30 [04/Aug/2025:11:56:57 +0800] "GET /api/report/mzlis HTTP/1.1" ************.7ms
110.157.71.244 [04/Aug/2025:11:56:59 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1376.9ms
************* [04/Aug/2025:12:10:48 +0800] "GET /api/patient/active HTTP/1.1" ************.4ms
************** [04/Aug/2025:12:10:48 +0800] "GET /api/patient/active HTTP/1.1" ************.4ms
************** [04/Aug/2025:12:10:48 +0800] "GET /api/patient/active HTTP/1.1" ************.5ms
39.144.196.28 [04/Aug/2025:12:10:48 +0800] "GET /api/patient/active HTTP/1.1" ************.8ms
112.43.85.169 [04/Aug/2025:12:10:50 +0800] "POST /open/wx/login HTTP/1.1" ************.9ms
************** [04/Aug/2025:12:10:52 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2090.4ms
39.144.209.207 [04/Aug/2025:12:11:05 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
39.144.209.207 [04/Aug/2025:12:11:10 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
39.144.209.207 [04/Aug/2025:12:11:13 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
*************** [04/Aug/2025:12:11:20 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.8ms
110.154.69.201 [04/Aug/2025:12:11:21 +0800] "GET /open/schedule/indexedDepartments HTTP/1.1" 200 3243 6541.3
ms
39.144.209.207 [04/Aug/2025:12:11:21 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
39.144.209.207 [04/Aug/2025:12:11:36 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
39.144.209.207 [04/Aug/2025:12:11:41 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
39.144.209.207 [04/Aug/2025:12:11:48 +0800] "GET /api/report/mzlis HTTP/1.1" 200 571 1926.4ms
************* [04/Aug/2025:12:11:49 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2617 1925.2ms
************* [04/Aug/2025:12:11:52 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1343.9ms
************** [04/Aug/2025:12:11:54 +0800] "GET /api/menzhen/bill/v1 HTTP/1.1" 200 4400 1107.0ms
************* [04/Aug/2025:12:11:56 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1958.3ms
************** [04/Aug/2025:12:11:59 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 1861.2ms
*************** [04/Aug/2025:12:12:13 +0800] "POST /api/weixinMip/createOrder/v2 HTTP/1.1" 200 1416 6164.8ms
************* [04/Aug/2025:12:12:16 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1867.4ms
*************** [04/Aug/2025:12:12:16 +0800] "POST /api/weixinMip/payOrder HTTP/1.1" 200 249 2119.3ms
************* [04/Aug/2025:12:12:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2617 2060.4ms
*************** [04/Aug/2025:12:12:24 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 540 1073.8ms
************** [04/Aug/2025:12:12:27 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 1641.2ms
*************** [04/Aug/2025:12:12:47 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4325 1120.4ms
************* [04/Aug/2025:12:12:48 +0800] "GET /open/schedule/queryDoctorByDays?departmentCode=120&date=202
5-08-04&days=7 HTTP/1.1" 200 9049 1056.1ms
************* [04/Aug/2025:12:12:51 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3395 2081.3ms
************* [04/Aug/2025:12:12:57 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3395 1898.6ms
************* [04/Aug/2025:12:13:00 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 4214 1052.6ms
************** [04/Aug/2025:12:13:05 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.1ms
*************** [04/Aug/2025:12:13:09 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.1m
s
************** [04/Aug/2025:12:13:11 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.1ms
************** [04/Aug/2025:12:13:24 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.5ms
39.144.197.31 [04/Aug/2025:12:13:25 +0800] "POST /api/reservation HTTP/1.1" ************.5ms
************ [04/Aug/2025:12:13:32 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2107.8ms
*************** [04/Aug/2025:12:13:45 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 1783 1013.9ms
************** [04/Aug/2025:12:13:53 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.7ms
************ [04/Aug/2025:12:14:03 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 2057.3ms
*************** [04/Aug/2025:12:14:14 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.2ms
************ [04/Aug/2025:12:14:16 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 1838.1ms
39.144.196.28 [04/Aug/2025:12:14:23 +0800] "POST /api/zhenjian/checkBalanceAndCreateWeixinRechargeOrder HTTP
/1.1" 200 368 1364.5ms
************** [04/Aug/2025:12:14:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1443.8ms
************** [04/Aug/2025:12:14:34 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.6ms
************ [04/Aug/2025:12:14:46 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 1900.5ms
************** [04/Aug/2025:12:14:54 +0800] "GET /api/menzhen/bill/v1 HTTP/1.1" 200 4400 1087.6ms
************** [04/Aug/2025:12:15:25 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1940.1ms
117.190.91.138 [04/Aug/2025:12:15:59 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1001 1843.9ms
112.43.24.155 [04/Aug/2025:12:16:13 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1760.9ms
112.43.24.155 [04/Aug/2025:12:16:17 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1263.9ms
************ [04/Aug/2025:12:16:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 1770.9ms
************** [04/Aug/2025:12:16:35 +0800] "GET /api/menzhen/bill/v1 HTTP/1.1" 200 4400 1001.0ms
106.123.171.74 [04/Aug/2025:12:16:56 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.9ms
************** [04/Aug/2025:12:17:08 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
************** [04/Aug/2025:12:17:19 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.3ms
************** [04/Aug/2025:12:17:39 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
110.152.201.47 [04/Aug/2025:12:17:41 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1419.0ms
43.242.154.194 [04/Aug/2025:12:17:43 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1774.7ms
110.152.201.47 [04/Aug/2025:12:17:47 +0800] "GET /api/report/mzlis HTTP/1.1" 200 3291 1903.9ms
************** [04/Aug/2025:12:17:49 +0800] "GET /api/report/mzlis HTTP/1.1" ************.0ms
************** [04/Aug/2025:12:18:08 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.4ms
116.178.232.175 [04/Aug/2025:12:18:15 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 2044.0ms
116.178.232.175 [04/Aug/2025:12:18:24 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1324.7ms
************** [04/Aug/2025:12:18:26 +0800] "GET /api/menzhen/bill/v1 HTTP/1.1" 200 4400 1044.2ms
************** [04/Aug/2025:12:18:28 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3984 1071.7ms
************** [04/Aug/2025:12:18:38 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.7ms
************** [04/Aug/2025:12:18:47 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3984 1033.9ms
************** [04/Aug/2025:12:18:49 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
************** [04/Aug/2025:12:18:50 +0800] "GET /api/erhcCard/getQrCode?patientId=1241876 HTTP/1.1" 200 468
 2378.0ms
************** [04/Aug/2025:12:18:56 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2130 2064.1ms
************* [04/Aug/2025:12:19:04 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.8ms
************** [04/Aug/2025:12:19:05 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2130 1955.9ms
116.178.232.175 [04/Aug/2025:12:19:16 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1802.3ms
39.144.209.194 [04/Aug/2025:12:19:24 +0800] "GET /api/erhcCard/getQrCode?patientId=1240903 HTTP/1.1" 200 422
 1912.3ms
************* [04/Aug/2025:12:19:29 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.1ms
124.88.48.230 [04/Aug/2025:12:19:35 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
*********** [04/Aug/2025:12:19:40 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1836.3ms
************* [04/Aug/2025:12:19:50 +0800] "POST /api/zhenjian/checkBalanceAndCreateWeixinRechargeOrder HTTP
/1.1" ************.8ms
124.88.48.230 [04/Aug/2025:12:19:57 +0800] "GET /api/report/mzlis HTTP/1.1" ************.4ms
************** [04/Aug/2025:12:19:57 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.3ms
************** [04/Aug/2025:12:20:01 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.8ms
124.88.48.230 [04/Aug/2025:12:20:10 +0800] "GET /api/report/mzlis HTTP/1.1" ************.2ms
110.154.64.86 [04/Aug/2025:12:20:12 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 5688 1063.9ms
124.88.48.230 [04/Aug/2025:12:20:18 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
110.154.64.231 [04/Aug/2025:12:20:19 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.8ms
110.154.64.86 [04/Aug/2025:12:20:24 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
************** [04/Aug/2025:12:20:36 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1194 1442.2ms
*********** [04/Aug/2025:12:20:39 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1017 1365.1ms
112.43.81.83 [04/Aug/2025:12:20:57 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1023 1771.2ms
110.154.64.86 [04/Aug/2025:12:20:59 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
112.43.81.83 [04/Aug/2025:12:21:01 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1023 1889.6ms
************** [04/Aug/2025:12:21:27 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.8ms
39.144.196.54 [04/Aug/2025:12:21:27 +0800] "GET /api/erhcCard/getQrCode?patientId=1242144 HTTP/1.1" 200 422 
1720.4ms
************** [04/Aug/2025:12:22:39 +0800] "GET /api/report/mzlis HTTP/1.1" ************.9ms
************* [04/Aug/2025:12:22:40 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2600 1915.8ms
************** [04/Aug/2025:12:22:52 +0800] "GET /api/report/mzlis HTTP/1.1" ************.5ms
************** [04/Aug/2025:12:22:59 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1804.7ms
************* [04/Aug/2025:12:23:00 +0800] "GET /api/erhcCard/getQrCode?patientId=1240722 HTTP/1.1" 200 422 
1901.3ms
************** [04/Aug/2025:12:23:04 +0800] "GET /api/report/mzlis HTTP/1.1" 200 52 1800.9ms
124.88.0.30 [04/Aug/2025:12:23:12 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
************** [04/Aug/2025:12:23:35 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46989 2116.5ms
39.144.209.218 [04/Aug/2025:12:23:47 +0800] "POST /api/erhcCard/getOrRegister HTTP/1.1" ************.7ms
124.88.0.30 [04/Aug/2025:12:23:49 +0800] "GET /api/report/mzlis HTTP/1.1" ************.8ms
124.88.0.30 [04/Aug/2025:12:23:50 +0800] "GET /api/report/mzlis HTTP/1.1" ************.3ms
124.88.0.30 [04/Aug/2025:12:23:54 +0800] "GET /api/report/mzlis HTTP/1.1" ************.6ms
************** [04/Aug/2025:12:24:06 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46989 1884.3ms
110.154.64.106 [04/Aug/2025:12:24:12 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2290 1954.9ms
************** [04/Aug/2025:12:24:32 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 46989 1350.2ms
************** [04/Aug/2025:12:24:37 +0800] "POST /open/wx/onWXPay HTTP/1.1" 200 101 6922.9ms
************ [04/Aug/2025:12:24:38 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 6155 1603.0ms
110.154.64.231 [04/Aug/2025:12:24:54 +0800] "GET /api/menzhen/bill HTTP/1.1" ************.4ms
************ [04/Aug/2025:12:25:33 +0800] "GET /api/report/mzlis HTTP/1.1" 200 2711 2019.7ms
49.112.226.16 [04/Aug/2025:12:25:40 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 3805 1010.0ms
************** [04/Aug/2025:12:25:47 +0800] "POST /open/wx/onWXPay HTTP/1.1" ************.6ms
49.112.227.135 [04/Aug/2025:12:25:58 +0800] "GET /api/erhcCard/getQrCode?patientId=1037520 HTTP/1.1" 200 422
 2225.1ms
39.144.196.2 [04/Aug/2025:12:26:10 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 2730 1880.9ms
39.144.32.112 [04/Aug/2025:12:26:29 +0800] "GET /api/zhuyuan/zylis HTTP/1.1" 200 9759 1407.0ms

```

---

<think><details><summary>Thought Process</summary>看起来脚本工作得很好！现在我可以创建一个新的slow.log文件，使用正确的阈值（毫秒为单位）：</details></think>

---

现在让我用正确的毫秒单位重新生成slow.log文件：

---

```bash
echo "=== 用正确的毫秒单位重新生成slow.log ===" && ./convert_log_to_ms.sh access_log.2025-08-04.log --slow 1000 > slow.log && echo "新的slow.log已生成，包含 $(wc -l < slow.log) 条响应时间超过1000毫秒的请求"
```

```
=== 用正确的毫秒单位重新生成slow.log ===
新的slow.log已生成，包含     1308 条响应时间超过1000毫秒的请求

```

---

<think><details><summary>Thought Process</summary>现在我们有了一个正确的slow.log文件，包含了真正的慢请求（响应时间超过1秒）。让我验证一下内容：</details></think>

---

```bash
echo "=== 验证新的slow.log内容 ===" && head -5 slow.log && echo "" && echo "=== 最慢的5个请求 ===" && sort -k8 -nr slow.log | head -5
```

```
=== 验证新的slow.log内容 ===
112.43.81.83 [04/Aug/2025:00:02:21 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1023 1862.2ms
106.123.170.79 [04/Aug/2025:00:02:25 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1328 1848.7ms
************* [04/Aug/2025:00:02:55 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1211 1375.9ms
************* [04/Aug/2025:00:03:36 +0800] "GET /api/report/mzlis HTTP/1.1" 200 1211 1853.8ms
106.123.146.21 [04/Aug/2025:00:04:42 +0800] "GET /api/menzhen/bill HTTP/1.1" 200 2246 6961.6ms

=== 最慢的5个请求 ===
112.43.109.206 [04/Aug/2025:11:47:03 +0800] "GET /api/zhuyuan/fee?days=7 HTTP/1.1" 200 ***********.8ms
49.118.199.117 [04/Aug/2025:11:45:42 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.2ms
39.144.33.253 [04/Aug/2025:10:07:57 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.7ms
39.144.196.114 [04/Aug/2025:11:09:54 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.8ms
117.190.91.138 [04/Aug/2025:10:12:49 +0800] "GET /img/health_card_template.png HTTP/1.1" 200 ***********.4ms

```

---

<think><details><summary>Thought Process</summary>现在我创建一个方案2的说明，如果用户希望直接在配置文件中修改：</details></think>

---

## ✅ 任务完成！响应时间单位已调整为毫秒

我已经为您完成了单位转换，现在访问日志中的响应时间以毫秒为单位显示。

## 📊 解决方案总结

### 方案1：使用转换脚本 ⭐ 已实现

我创建了一个转换脚本 `convert_log_to_ms.sh`，可以实时将微秒转换为毫秒显示：

#### 使用方法：
```bash
# 查看转换后的日志（毫秒单位）
./convert_log_to_ms.sh access_log.2025-08-04.log

# 只显示慢请求（超过1000毫秒）
./convert_log_to_ms.sh access_log.2025-08-04.log --slow 1000

# 实时监控日志（毫秒单位）
./convert_log_to_ms.sh access_log.2025-08-04.log --tail
```

#### 新的slow.log：
- **记录数量**: 1,308条响应时间超过1秒的请求
- **格式**: 已转换为毫秒显示（如 `1862.2ms`）
- **最慢请求**: 6.39秒的住院费用查询

### 方案2：修改配置文件（如果需要）

如果您希望直接在日志配置中修改，有以下几个选择：

1. **更改为秒单位**（会失去精度）：
   ```yaml
   pattern: "%a %t \"%r\" %s %b %T"  # %T = 秒
   ```

2. **保持当前格式**，配合转换脚本使用（推荐）

## 🎯 现在的日志分析结果

**真实性能表现**：
- 96%的请求在0.5秒内完成 ✅
- 只有1,308个请求（1.7%）超过1秒 ⚠️
- 真正的性能问题主要集中在：
  - 住院费用查询API
  - 大图片资源加载
  - 复杂的数据查询

这个转换工具让您可以用熟悉的毫秒单位来分析性能，同时保持了原始数据的精度！

---

