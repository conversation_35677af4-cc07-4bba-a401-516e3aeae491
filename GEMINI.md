# Gemini Code Assistant Context

This document provides context for the Gemini Code Assistant to understand the `zhangyi-xjetyy` project.

## Project Overview

This is a Java/Kotlin web application for the Xinjiang Children's Hospital, built upon the RuoYi framework. The project is a multi-module Maven project that uses Spring Boot, MySQL, and Redis. It provides various services for patients, including appointment booking, payment, and viewing medical records.

The project is divided into the following Maven modules:

*   `ruoyi-admin`: The main web application and entry point.
*   `ruoyi-framework`: The core framework module.
*   `ruoyi-system`: The system management module.
*   `ruoyi-quartz`: The Quartz scheduler for timed tasks.
*   `ruoyi-generator`: The code generator.
*   `ruoyi-common`: Common utilities.
*   `xydlfy-ph`: A custom module containing the hospital-specific business logic.
*   `ph-common`: Common utilities for the `xydlfy-ph` module.

## Building and Running

The project is built using Maven.

**To build the project:**

```bash
mvn clean package -Dmaven.test.skip=true
```

The final application artifact is `ruoyi-admin/target/zyxcx.jar`.

**To run the project in a development environment:**

You can run the application by executing the `main` method in the `com.ruoyi.RuoYiApplication` class, located in the `ruoyi-admin` module, from your IDE. When running locally, ensure the appropriate Spring profile (e.g., `druid`) is active.

**To run the project in the production environment:**

**Note:** The following commands and scripts (`deploy.sh`, `zyxcx.service`) are for the production environment only and will not work on a local development machine.

```bash
# (On production server)
# To build and deploy the latest version
./deploy.sh
```

The project is run as a systemd service named `zyxcx`. The `deploy.sh` script handles the entire build and deployment process. The `zyxcx.service` file defines the production runtime environment, including the user (`ecs-user`), JVM options, and logging paths.

```bash
# (On production server)
# Start the service
sudo systemctl start zyxcx

# (On production server)
# Stop the service
sudo systemctl stop zyxcx
```

## Development Conventions

### Session Management

The project has a well-defined session management system for mobile clients (WeChat/Alipay), which is documented in `docs/mobile-session-management-guide.md`. It uses a system of interceptors and annotations to handle authentication and authorization.

*   `@RequireSession`: Requires a valid user session.
*   `@RequireActivePatient`: Requires the user to have an active patient record.

### Third-Party Services

The application integrates with several third-party services on the hospital's internal network. These services are not accessible from the development machine. Therefore, it is not possible to run the full application or all tests locally. The `README.md` file contains a list of these services and their endpoints.
