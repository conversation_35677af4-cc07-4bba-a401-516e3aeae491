# 移动端会话（患者）管理开发规范

## 概述

本文档总结了项目中移动端会话管理系统的设计规范，旨在帮助开发者快速理解架构设计，避免常见陷阱，遵循最佳实践。

## 🎯 核心架构

### 数据结构关系

```kotlin
Session (会话) 
├── openId: String           // 用户唯一标识
├── clientType: ClientType   // WEIXIN/ALIPAY
└── 关联多种Patient类型
    ├── Patient              // 通用患者（门诊/住院）
    ├── ZhuyuanPatient      // 住院专用患者
    ├── PscNurseSession     // 护士会话
    └── PscCarerSession     // 陪护员会话
```

### 拦截器链设计

```mermaid
sequenceDiagram
    participant HTTP请求
    participant SessionInterceptor
    participant 路径分发
    participant 具体验证器
    participant Controller

    HTTP请求->>SessionInterceptor: 请求
    SessionInterceptor->>路径分发: 根据URL分发
    路径分发-->>具体验证器: /api/zhuyuan/** → ActiveZhuyuanPatientInterceptor
    路径分发-->>具体验证器: /api/psc/** → PscSessionInterceptor
    路径分发-->>具体验证器: /api/** → ActivePatientInterceptor
    具体验证器->>Controller: 验证通过
```

## 📝 开发规范

### 1. 注解使用规范

#### 基础会话验证
```kotlin
// 任意客户端
@RequireSession
fun basicApi(): AjaxResult

// 指定客户端类型
@RequireSession(clientType = ClientType.WEIXIN)
fun weixinOnlyApi(): AjaxResult

@RequireSession(clientType = ClientType.ALIPAY)
fun alipayOnlyApi(): AjaxResult
```

#### 患者身份验证
```kotlin
// 需要门诊卡绑定
@RequireActivePatient(shouldBeMenzhenPatient = true)
fun menzhenApi(): AjaxResult

// 需要住院号绑定
@RequireActivePatient(shouldBeZhuyuanPatient = true)
fun zhuyuanApi(): AjaxResult

// 同时需要门诊和住院身份
@RequireActivePatient(
    shouldBeMenzhenPatient = true,
    shouldBeZhuyuanPatient = true
)
fun fullPatientApi(): AjaxResult
```

#### 专业角色验证
```kotlin
@RequirePscNurseSession
fun nurseApi(): AjaxResult

@RequirePscCarerSession
fun carerApi(): AjaxResult
```

### 2. 数据获取标准化

```kotlin
class SomeController {
    
    @RequireSession
    fun example(): AjaxResult {
        // ✅ 推荐：使用扩展函数
        val session = request.getClientSession()
        val patient = request.getCurrentPatient()
        val zhuyuanPatient = request.getCurrentZhuYuanPatient()
        
        // ❌ 避免：直接getAttribute
        val session = request.getAttribute("client_session") as Session
        
        return AjaxResult.success(patient)
    }
}
```

### 3. 路径规范

| 路径前缀 | 功能说明 | 自动注入数据 | 适用场景 |
|---------|---------|-------------|----------|
| `/api/zhuyuan/**` | 住院相关功能 | `ZhuyuanPatient` | 住院充值、费用查询、检查报告 |
| `/api/psc/**` | 护理相关功能 | `PscNurseSession`<br/>`PscCarerSession` | 护士站、陪护管理 |
| `/api/**` | 通用患者功能 | `Patient` | 预约挂号、门诊缴费、个人信息 |

### 4. 错误处理规范

```kotlin
// ✅ 使用预定义常量
ServletUtils.renderString(response, Values.INVALID_CLIENT_SESSION_AJAX_RESULT)
ServletUtils.renderString(response, Values.REQUIRE_WEIXIN_SESSION_AJAX_RESULT)
ServletUtils.renderString(response, Values.REQUIRE_ALIPAY_SESSION_AJAX_RESULT)

// ❌ 避免硬编码字符串
ServletUtils.renderString(response, """{"code":500,"msg":"会话无效"}""")
```

## 🚀 最佳实践

### Controller 开发模板

```kotlin
@RestController
@RequestMapping("/api/example")
class ExampleController : BaseController() {
    
    // 基础API - 只需要有效会话
    @GetMapping("/info")
    @RequireSession
    fun getInfo(): AjaxResult {
        val session = request.getClientSession()
        return AjaxResult.success(mapOf("openId" to session.openId))
    }
    
    // 需要患者身份的API
    @PostMapping("/submit")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun submitData(@RequestBody data: SomeDto): AjaxResult {
        val patient = request.getCurrentPatient()
        // 业务逻辑...
        return AjaxResult.success()
    }
    
    // 特定客户端API
    @PostMapping("/wxpay")
    @RequireSession(clientType = ClientType.WEIXIN)
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun wxpay(@RequestParam amount: Long): AjaxResult {
        val patient = request.getCurrentZhuYuanPatient()
        // 微信支付逻辑...
        return AjaxResult.success()
    }
}
```

### 数据验证最佳实践

```kotlin
// ✅ 在拦截器级别验证身份，Controller专注业务逻辑
@RequireActivePatient(shouldBeZhuyuanPatient = true)
fun zhuyuanApi(): AjaxResult {
    val patient = request.getCurrentZhuYuanPatient() // 确保非null
    // 直接使用patient，无需null检查
}

// ❌ 避免在Controller中重复验证
fun badApi(): AjaxResult {
    val session = request.getClientSession()
    if (session == null) {
        return AjaxResult.error("会话无效") // 这应该由拦截器处理
    }
}
```

## ⚠️ 常见陷阱与注意事项

### 1. 路径映射冲突

```kotlin
// ⚠️ 注意路径排除配置
registry.addInterceptor(activePatientInterceptor)
    .addPathPatterns("/api/**")
    .excludePathPatterns("/api/psc/**", "/api/zhuyuan/**")  // 必须排除
```

### 2. 会话缓存机制

```kotlin
// ✅ 理解缓存逻辑 - ServletExt.kt
fun HttpServletRequest.getClientSession(): Session? {
    // 先检查Request缓存
    var session: Session? = this.getAttribute(CLIENT_SESSION) as Session?
    val sessionLoaded: Boolean = this.getAttribute(CLIENT_SESSION_LOADED) as Boolean? ?: false
    
    // 避免重复数据库查询
    if (session == null && !sessionLoaded) {
        // 从Header获取Authorization进行数据库查询
        val sid: String? = this.getHeader(HttpHeaders.AUTHORIZATION)
        // ...
    }
}
```

### 3. 客户端类型判断

```kotlin
// ✅ 正确的客户端类型检查
if (requireSession.clientType == ClientType.WEIXIN && !session.isWeixinSession) {
    // 返回错误
}

// ❌ 避免字符串比较
if (session.clientType == "weixin") { // 类型不安全
```

## 🔧 代码质量改进建议

### 当前"技术债务"

1. **ActiveZhuyuanPatientInterceptor.kt:23** - 删除注释代码
```kotlin
// ❌ 删除这行注释
// val requireActivePatient = handler.method.getAnnotation(RequireActivePatient::class.java) ?: return true
```

2. **ZhuyuanPatientApiController** - 方法过长需要重构
- `wxpay()` - 44行，建议拆分为验证+支付+响应
- `alipay()` - 46行，同上
- `refund()` - 应该使用策略模式统一处理

### 推荐重构方案

```kotlin
// 支付服务统一抽象
interface PaymentService {
    fun createPayment(patient: Patient, amount: Long): PaymentResult
}

class WeixinPaymentService : PaymentService {
    override fun createPayment(patient: Patient, amount: Long): PaymentResult {
        // 微信支付逻辑
    }
}

class AlipayPaymentService : PaymentService {
    override fun createPayment(patient: Patient, amount: Long): PaymentResult {
        // 支付宝支付逻辑  
    }
}

// Controller变得简洁
@PostMapping("pay")
@RequireActivePatient(shouldBeZhuyuanPatient = true)
fun pay(
    @RequestParam amount: Long,
    @RequestParam payType: String
): AjaxResult {
    val patient = request.getCurrentZhuYuanPatient()
    val paymentService = paymentServiceFactory.getService(payType)
    val result = paymentService.createPayment(patient, amount)
    return result.toAjaxResult()
}
```

## 📚 相关文件索引

| 文件路径 | 主要职责 | 关键类/方法 |
|---------|---------|-----------|
| `annotation/RequireSession.kt` | 会话验证注解 | `@RequireSession` |
| `annotation/RequireActivePatient.kt` | 患者身份验证注解 | `@RequireActivePatient` |
| `interceptor/SessionInterceptor.kt` | 基础会话验证 | `preHandle()` |
| `interceptor/ActivePatientInterceptor.kt` | 患者身份验证 | `preHandle()` |
| `interceptor/ActiveZhuyuanPatientInterceptor.kt` | 住院患者验证 | `preHandle()` |
| `ext/ServletExt.kt` | 数据获取扩展函数 | `getClientSession()` |
| `configuration/WebConfiguration.kt` | 拦截器注册配置 | `addInterceptors()` |
| `ph-common/common/ClientType.kt` | 客户端类型枚举 | `WEIXIN`, `ALIPAY` |
| `ph-common/common/Values.kt` | 常量定义 | 错误信息常量 |

## 💡 开发建议

1. **新API开发时，先确定路径前缀** - 这决定了使用哪个拦截器
2. **优先使用注解驱动** - 让拦截器处理验证，Controller专注业务
3. **使用标准化扩展函数** - 避免重复的getAttribute调用
4. **错误信息使用预定义常量** - 保持用户体验一致性
5. **遵循单一职责原则** - 一个方法只做一件事，避免超过3层缩进

---

**记住：好的代码不是炫技，而是让下一个接手的人能快速理解和维护。**
