# 第三方企业访问移动影像接口说明

## 访问流程图

```mermaid
sequenceDiagram
    participant Client as 第三方客户端
    participant AuthServer as 第三方服务器
    participant TokenService as token服务
    participant ImageService as 移动影像服务

    Client->>AuthServer: 获取接口Token
    AuthServer->>TokenService: 申请Token
    TokenService-->>AuthServer: 返回Token
    AuthServer-->>Client: 返回Token或接口全路径
    
    Client->>ImageService: 访问页面接口
    ImageService-->>Client: 返回页面
```

## 流程说明

1. **Token获取阶段**
   - 第三方客户端向第三方服务器请求获取接口Token
   - 第三方服务器向token服务申请Token
   - token服务返回Token给第三方服务器
   - 第三方服务器将Token或接口全路径返回给客户端

2. **页面访问阶段**
   - 第三方客户端使用获取的Token访问移动影像服务的页面接口
   - 移动影像服务返回相应的页面内容

## API接口详情

### 获取Token接口

**接口地址：** `POST https://film.radonline.cn/moi_gw/user/rad/user/clientLoginByNP`

**请求头：**
```
Content-Type: application/json
```

**请求参数：**
```json
{
    "username": "xjzzqet",
    "password": "xjzzqet@123"
}
```

**请求示例：**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "username": "xjzzqet",
    "password": "xjzzqet@123"
  }' \
  https://film.radonline.cn/moi_gw/user/rad/user/clientLoginByNP
```

**响应示例：**
```json
{
    "code": "SUCCESS",
    "msg": "OK",
    "subCode": "SUCCESS",
    "subMsg": "OK",
    "timestamp": 1755641858243,
    "data": "224c4167-bb20-414d-9aad-70240c383509",
    "success": true,
    "fail": false
}
```

**响应参数说明：**
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | String | 响应状态码，SUCCESS表示成功 |
| msg | String | 响应消息 |
| subCode | String | 子状态码 |
| subMsg | String | 子消息 |
| timestamp | Long | 时间戳 |
| data | String | 返回的Token值 |
| success | Boolean | 是否成功 |
| fail | Boolean | 是否失败 |

**注意事项：**
- 返回的 `data` 字段即为Token值，用于后续接口调用
- Token具有时效性，请妥善保存并及时使用

### 页面访问接口

**页面链接：** `https://film.radonline.cn/web/fore-end/patient.html`

**完整URL格式：**
```
https://film.radonline.cn/web/fore-end/patient.html?token=<token>&unitid=<unitid>&xeguid=<xeguid>&hasList=true&callBack=true#/check-detail
```

**URL参数说明：**
| 参数名称 | 类型 | 是否必须 | 描述 |
|----------|------|----------|------|
| token | 访问令牌 | 是 | 用于访问验证的令牌，通过获取Token接口获取 |
| unitid | 医院标识 | 是 | 用于标识一个医院的id，需与调用方进行统一约定 |
| xeguid | 检查标识 | 是 | 检查号（一般是身份证号，需要使用约定的加密方式进行加密） |
| hasList | 是否支持列表 | 否 | 支持返回多条结果，默认不设置直接会进入详情 |
| callBack | 返回按钮标识 | 否 | 默认不填写，如果需要列表页有返回按钮将其设置为true |

**使用示例：**
```
https://film.radonline.cn/web/fore-end/patient.html?token=224c4167-bb20-414d-9aad-70240c383509&unitid=hospital001&xeguid=encrypted_exam_id&hasList=true&callBack=true#/check-detail
```

**注意事项：**
- `token` 参数使用获取Token接口返回的值
- `xeguid` 参数需要按照约定的加密方式进行加密处理
- `hasList=true` 时支持返回多条检查结果
- `callBack=true` 时页面会显示返回按钮
- URL末尾的 `#/check-detail` 为页面路由，用于直接进入检查详情页面

## 加密方法实现

### xeguid参数加密

针对URL参数中的 `xeguid` 检查标识需要进行RSA加密处理，以下提供Java实现示例：

**加密方法：**

```java
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RSAEncryptionUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(RSAEncryptionUtil.class);
    
    // RSA加密算法常量
    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    
    // 公钥字符串 - 实际使用时需要替换为真实的公钥
    private static final String PUBLIC_KEY = "YOUR_BASE64_ENCODED_PUBLIC_KEY_HERE";
    
    /**
     * 使用RSA公钥加密数据
     * 
     * @param data 待加密的原始数据
     * @return 加密后的Base64编码字符串，加密失败时返回null
     */
    public static String encrypt(String data) {
        if (data == null || data.isEmpty()) {
            logger.warn("加密数据为空，返回null");
            return null;
        }
        
        try {
            // 解码Base64编码的公钥
            byte[] publicKeyBytes = Base64.getDecoder().decode(PUBLIC_KEY);
            
            // 生成RSA公钥对象
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);
            
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            
            // 执行加密并返回Base64编码结果
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            String result = Base64.getEncoder().encodeToString(encryptedBytes);
            
            logger.debug("RSA加密成功，数据长度: {}", data.length());
            return result;
            
        } catch (Exception e) {
            logger.error("RSA加密失败，数据: [{}]", data, e);
            return null;
        }
    }
    
    /**
     * 加密检查号用于API调用
     * 
     * @param examId 原始检查号
     * @return 加密后的检查号，用作xeguid参数
     */
    public static String encryptExamId(String examId) {
        logger.info("开始加密检查号，类型: 身份证号");
        String encryptedResult = encrypt(examId);
        
        if (encryptedResult != null) {
            logger.info("检查号加密成功");
        } else {
            logger.warn("检查号加密失败，请检查输入数据和公钥配置");
        }
        
        return encryptedResult;
    }
}
```

**重要提示：**
- 请将 `YOUR_BASE64_ENCODED_PUBLIC_KEY_HERE` 替换为实际的RSA公钥
- 代码已集成SLF4J日志框架，支持多种日志级别和输出方式
- 公钥建议通过配置文件或环境变量管理，避免硬编码
- 生产环境建议将日志级别设置为INFO或WARN，避免敏感信息泄露
- 建议添加单元测试验证加密功能的正确性

