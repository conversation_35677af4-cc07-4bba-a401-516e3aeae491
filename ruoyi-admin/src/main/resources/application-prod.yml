ruoyi:
  profile: D:/zyxcx/assets

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 主库数据源
      master:
        url: ********************************************************************************************************************************************************************************************************************
        driverClassName: com.mysql.cj.jdbc.Driver
        username: xjetyy
        password: nMoUUuWg8HHuQcNoFQTD8GDoQ4rNFpPz
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      pacs:
        url: ***************************************************************************************************************
        driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
        username: ZY
        password: Xjetvm@2021
        enabled: true
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 3000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      keep-alive: true
      pool-prepared-statements: true

# 开发环境配置
server:
  max-http-request-header-size: 2KB
  connection-timeout: 30000
  httpPort: 8899 # HTTP 端口
  port: 8888 # HTTPS 端口
  ssl:
    key-store: classpath:xj-etyy.com.jks
    key-store-password: qwUbiMIE
    keyStoreType: JKS
    ciphers: TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_AES_128_SHA256,TLS_ECDHE_ECDSA_WITH_AES_128_SHA256,TLS_ECDHE_RSA_WITH_AES_128_SHA,TLS_ECDHE_ECDSA_WITH_AES_128_SHA,TLS_ECDHE_RSA_WITH_AES_256_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_SHA384,TLS_ECDHE_RSA_WITH_AES_256_SHA,TLS_ECDHE_ECDSA_WITH_AES_256_SHA
  servlet:
    # 应用的访问路径
    context-path: /
    session:
      cookie:
        http-only: true
        secure: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    threads.max: 800
    # Tomcat启动初始化的线程数，默认值25
    threads.min-spare: 30
    basedir: D:/zyxcx/tomcat
    accesslog:
      enabled: true
      suffix: .log
      prefix: access_log
      file-date-format: .yyyy-MM-dd
      directory: logs
      pattern: "%a %t \"%r\" %s %b %D"
  forward-headers-strategy: native

app:
  domain: zy.xj-etyy.com
  baseUrl: https://zy.xj-etyy.com:8888
  authCode: 4GCeCRCVRquc