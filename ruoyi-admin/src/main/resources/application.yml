# 项目相关配置
ruoyi:
  # 名称
  name: XYDLFY-ZYXCX
  # 版本
  version: 4.7.7
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /Users/<USER>/Project/zhangyi-xyd6fy/upload
  # 获取ip地址开关
  addressEnabled: false

# 开发环境配置
server:
  httpPort: 8899
  port: 8899
  servlet:
    # 应用的访问路径
    context-path: /
    session:
      cookie:
        # 设置HttpOnly属性
        httpOnly: true
        # 设置Cookie的secure属性
        secure: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
    accesslog:
      enabled: true
      suffix: .log
      prefix: access_log
      file-date-format: .yyyy-MM-dd
      pattern: "%a %t \"%r\" %s %b %D"

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn
    cn.binarywang.wx: debug
    com.github.binarywang: debug
    space.lzhq: debug

# 用户配置
user:
  password:
    # 密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5

# Spring配置
spring:
  sql:
    init:
      enabled: false
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: static/i18n/messages
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  mvc:
    format:
      date: yyyy-MM-dd
      date-time: yyyy-MM-dd HH:mm:ss
      time: HH:mm:ss
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  threads:
    virtual:
      enabled: true

# MyBatis
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain,space.lzhq.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Shiro
shiro:
  user:
    # 登录地址
    loginUrl: /login
    # 权限认证失败地址
    unauthorizedUrl: /unauth
    # 首页地址
    indexUrl: /index
    # 验证码开关
    captchaEnabled: true
    # 验证码类型 math 数组计算 char 字符
    captchaType: math
  cookie:
    # 设置Cookie的域名 默认空，即当前访问的域名
    domain:
    # 设置cookie的有效访问路径
    path: /
    # 设置HttpOnly属性
    httpOnly: true
    # 设置Cookie的secure属性
    secure: true
    # 设置Cookie的过期时间，天为单位
    maxAge: 30
    # 设置密钥，务必保持唯一性（生成方式，直接拷贝到main运行即可）KeyGenerator keygen = KeyGenerator.getInstance("AES"); SecretKey deskey = keygen.generateKey(); System.out.println(Base64.encodeToString(deskey.getEncoded()));
    cipherKey: o0so+Lg9iV0xOI1eA6M0CQ==
  session:
    # Session超时时间，-1代表永不过期（默认30分钟）
    expireTime: 30
    # 同步session到数据库的周期（默认1分钟）
    dbSyncPeriod: 1
    # 相隔多久检查一次session的有效性，默认就是10分钟
    validationInterval: 10
    # 同一个用户最大会话数，比如2的意思是同一个账号允许最多同时两个人登录（默认-1不限制）
    maxSession: -1
    # 踢出之前登录的/之后登录的用户，默认踢出之前登录的用户
    kickoutAfter: false
  rememberMe:
    # 是否开启记住我
    enabled: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*,/ph/article/*,/ph/wikiArticle/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/ph/*,/api/jubao/*

wx:
  miniapp:
    appid: wxccb0430de6991c6d
    secret: 15cacbc67004174f603f60b157ae0aa8
    config-storage:
      type: memory
  pay:
    appId: wxccb0430de6991c6d
    mchId: 1610072859
    mchKey: XinJiangErTongYiYuan161007285900
    subAppId:
    subMchId:
    keyPath: classpath:apiclient_cert.p12
    # v3
    apiV3Key: 2mB6RKZJXzJUPhrjpCA6sNFNNxNMNUW9
    certSerialNo: 3DB26475690AA803E74DA419E61E314864A0F0C7
    privateKeyPath: classpath:1610072859_20250423_key.pem
    privateCertPath: classpath:1610072859_20250423_cert.pem
  subscribe-message:
    yuyue-id: BwR2dJ5eqaVUJi0j89iLIWv8Mo9eM-WrTyxD626D3oA
    yuyue-page: pages_yuyue/YuyueHistory/YuyueHistory
    cancel-yuyue-id: hihPPRnvzV4cFfUn8lXhCLC-DOCWypk94mos_Z7WYe8
    cancel-yuyue-page: pages_yuyue/YuyueHistory/YuyueHistory
    # 就诊提醒
    visiting-reminder-id: nkl-MdIUcyFewDp_zb9TeEg3iyjQJDRgKAbbEoxKh-g
    visiting-reminder-page: pages_yuyue/YuyueHistory/YuyueHistory
    # 停诊通知
    stop-clinic-id: aeRXMdiMYSUVDO2aHem4eaw3LONf2KeAQInXg61ahCY
    stop-clinic-page: pages_yuyue/YuyueHistory/YuyueHistory
    # 确认收款
    receive-transfer-id: DXgCZ49C4CcqoD1C92tvarf0kaybOX7xEDrVaIT_d0k
    receive-transfer-page: pages_menzhen/confirmCardRefund/confirmCardRefund

alipay:
  appId: 2021003144629326
  merchantName: 新疆维吾尔自治区儿童医院
  merchantId: 2088341690455593
  pid: 2088341243702554
  encryptType: AES
  encryptKey: x8lw+OAzbcNDOwqhiL121w==
  notifyUrl: https://zy.xj-etyy.com:8888/open/alipay/onPay
  # 关于密钥和证书的文档：
  # https://opendocs.alipay.com/open/291/105971
  # https://opendocs.alipay.com/open/291/105974
  # 应用私钥（private key）
  merchantPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCGvuUf0HVLvMu8VB9to7wJj2vO8if5jAzgTZ+qAsZmJ6INWBc6Tf14e1BWHTMJbFPa+pTZmzMJ7FdV+jUPwg5c+DPUvQaLCm3KkdYq4MNid2RtOW8NrExSdupGwkSshf3tHePFT4VwYmvI+q4JWiPGWpFKkybsE0gnhuhnZusWeNg0R/PWq5bYMYlE5ux797pFoBHZRlD0O0VPPcL9m5b0eEPoHcQ4hnj4H71g/pRLXrMTQ9iDIcaxe3A/iiw/dcL90AMwkTayLCrCHzpVTyVT9BXb0dkkCg3WMIHcS+jEUljcovJ5mXNSlJ/BxWRYGXAm791dkktgPFNOYj/0VyGJAgMBAAECggEAelbyEzuic9x7oodCbbp+rLNyL9f/YXy96B2yPJr+ZZvQb4sus7pBy9UycK1cPE8Fp8lhwl0+LuFNvntJGACaJ+EWti3VtgIHAyf8KrwdydYa8Wx3HxPzy9+5//q8fWO+1D4SIoB76DkEIdswjr8vwhVS20HV0fwDxBeiBNgV1DUOhTKzEH6vgNLq6e04rNsPHn3jGDVSBzhuH5aFgtstVKq7HVgqVvVXe0IUDmBLUEUYKofE5xOhc21MGgIm+F1HUOtLbQnw4to8+706pm2lMGNdLIMgKcEzbdSzdQrzJUCTholCtAWlAwAeESMdWKuhafLso9E+PkyevgY14vUVrQKBgQD4XibevJIXgC5z8aWTNeOp066ON2odhGPh/LAdBoLknt7pM1XCwCDqYv97BWuAOARg9MjZroelMba8+yQT9CCUSyx4xHgZpHxsOMI7/1FCLjM/V5NjUikW3468pe3PPQmj2m2vPlnMYHW978oXCuh6/syRbEFZw5mEH4kH8pHadwKBgQCK4uf33NcVMNv44QmAkhacfuFi+jTIyxh2kQzUCpDUPLkM4sJAKJyiF0SvernbPnkaFUp/fLvb0H5a9gkZxr5GsxM1t5FKQjeTzC5x+GyJHO5hNeKEYAMuHVA5JudBoR3CFBtHrKxPnEw3Fz96fm1J2o/DiO8m9CvWZ19UoSHj/wKBgQCPYXs7Fs2X943ZrWUSyQd3IRciTudZxvYKnl/lQb8K0JSLN/GIjnXyGdi3Ynee+7e7aVUSJel0+nAuRSWcx1CPi55O0c0rsIOLznCDRm/Yrucy6BHUQrgp1kGGspvJCcmo6A5uHqakkAMo9WdLSibP6oyaX06SzZKwbawkBGsjHQKBgBJp7RlmtTmBBdkkdO8P72GGTPmjdLYefzrRFmNSl0scVeiIc/qvp7yx2xBs9BFGYiX8JqlqbznAldAXiLNbL0UOXwCjOuceimHVE19W8YToSg6nZ0XnuOQ3TLiXGNgRtXFS2x8QnLPdZBl1wpvKAVFmYeMp15NRkjJV+ESvLGAfAoGBAMUOa1F3043Lil5WL++b+/DcahyLL/SAxU+qTXTaETKRxwcgdGKmle5ibaOYSSKqkyTT/8HGOIAiVTVN1+0GfjlqIn93IICBUuEoeiSK+kL7sN66lk8W0S6LJVe7pRz4s631XbBzPj9CGhAIJlumuPFGV89Fc7+83D6twz1zJHJv
  # 应用公钥证书（public key cert）
  merchantCertPath: D:\\zyxcx\\cert\\appCertPublicKey_2021003144629326.cer
  # 支付宝公钥证书（alipay public key cert）
  alipayCertPath: D:\\zyxcx\\cert\\alipayCertPublicKey_RSA2.cer
  # 支付宝根证书（alipay root cert）
  alipayRootCertPath: D:\\zyxcx\\cert\\alipayRootCert.cer
  charset: utf-8
  signType: RSA2
  format: json
  serverUrl: https://openapi.alipay.com/gateway.do
  protocol: https
  # 定点医疗机构应用ID，即医药机构渠道编号，参见反馈单中的渠道编号
  mipOrgAppId: 1G9JFCNMQ0BR3F60C80A000008D61B35
  # 机构渠道认证编码，参见反馈单中的机构渠道认证编码
  mipOrgChnlCrtfCode: BqK1kMStlhVDgN2uHf4EsP9BQzovPtfTZGwRsyhqMMbq4tTekl4i8uxRHhJzeiuw
  # 定点医药机构编码，参见反馈单中的定点医药机构编码
  mipOrgCode: H65010200028
  # 授权成功后回调医药机构前端地址
  mipCallUrl: alipays://platformapi/startapp?appId=2021003144629326&page=pages_yibao%2FmipAuth%2FmipAuth

app:
  domain: zy.xj-etyy.com
  baseUrl: https://zy.xj-etyy.com:8888
  authCode: 4GCeCRCVRquc