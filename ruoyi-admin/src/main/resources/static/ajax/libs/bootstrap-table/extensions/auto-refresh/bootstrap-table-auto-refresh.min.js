/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.17.1
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t,e){return t(e={exports:{}},e.exports),e.exports}var r=function(t){return t&&t.Math==Math&&t},o=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),c={}.propertyIsEnumerable,f=Object.getOwnPropertyDescriptor,a={f:f&&!c.call({1:2},1)?function(t){var e=f(this,t);return!!e&&e.enumerable}:c},s=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},l={}.toString,p=function(t){return l.call(t).slice(8,-1)},h="".split,y=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?h.call(t,""):Object(t)}:Object,d=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},b=function(t){return y(d(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},g=function(t,e){if(!v(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!v(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!v(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!v(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")},m={}.hasOwnProperty,w=function(t,e){return m.call(t,e)},O=o.document,j=v(O)&&v(O.createElement),S=function(t){return j?O.createElement(t):{}},R=!u&&!i((function(){return 7!=Object.defineProperty(S("div"),"a",{get:function(){return 7}}).a})),T=Object.getOwnPropertyDescriptor,P={f:u?T:function(t,e){if(t=b(t),e=g(e,!0),R)try{return T(t,e)}catch(t){}if(w(t,e))return s(!a.f.call(t,e),t[e])}},x=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},A=Object.defineProperty,E={f:u?A:function(t,e,n){if(x(t),e=g(e,!0),x(n),R)try{return A(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},_=u?function(t,e,n){return E.f(t,e,s(1,n))}:function(t,e,n){return t[e]=n,t},I=function(t,e){try{_(o,t,e)}catch(n){o[t]=e}return e},k=o["__core-js_shared__"]||I("__core-js_shared__",{}),M=Function.toString;"function"!=typeof k.inspectSource&&(k.inspectSource=function(t){return M.call(t)});var F,C,B,L=k.inspectSource,N=o.WeakMap,q="function"==typeof N&&/native code/.test(L(N)),z=n((function(t){(t.exports=function(t,e){return k[t]||(k[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),D=0,W=Math.random(),$=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++D+W).toString(36)},G=z("keys"),K=function(t){return G[t]||(G[t]=$(t))},Q={},V=o.WeakMap;if(q){var X=new V,Y=X.get,H=X.has,J=X.set;F=function(t,e){return J.call(X,t,e),e},C=function(t){return Y.call(X,t)||{}},B=function(t){return H.call(X,t)}}else{var U=K("state");Q[U]=!0,F=function(t,e){return _(t,U,e),e},C=function(t){return w(t,U)?t[U]:{}},B=function(t){return w(t,U)}}var Z,tt,et={set:F,get:C,has:B,enforce:function(t){return B(t)?C(t):F(t,{})},getterFor:function(t){return function(e){var n;if(!v(e)||(n=C(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}}},nt=n((function(t){var e=et.get,n=et.enforce,r=String(String).split("String");(t.exports=function(t,e,i,u){var c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,a=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof e||w(i,"name")||_(i,"name",e),n(i).source=r.join("string"==typeof e?e:"")),t!==o?(c?!a&&t[e]&&(f=!0):delete t[e],f?t[e]=i:_(t,e,i)):f?t[e]=i:I(e,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||L(this)}))})),rt=o,ot=function(t){return"function"==typeof t?t:void 0},it=function(t,e){return arguments.length<2?ot(rt[t])||ot(o[t]):rt[t]&&rt[t][e]||o[t]&&o[t][e]},ut=Math.ceil,ct=Math.floor,ft=function(t){return isNaN(t=+t)?0:(t>0?ct:ut)(t)},at=Math.min,st=function(t){return t>0?at(ft(t),9007199254740991):0},lt=Math.max,pt=Math.min,ht=function(t){return function(e,n,r){var o,i=b(e),u=st(i.length),c=function(t,e){var n=ft(t);return n<0?lt(n+e,0):pt(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},yt={includes:ht(!0),indexOf:ht(!1)}.indexOf,dt=function(t,e){var n,r=b(t),o=0,i=[];for(n in r)!w(Q,n)&&w(r,n)&&i.push(n);for(;e.length>o;)w(r,n=e[o++])&&(~yt(i,n)||i.push(n));return i},bt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],vt=bt.concat("length","prototype"),gt={f:Object.getOwnPropertyNames||function(t){return dt(t,vt)}},mt={f:Object.getOwnPropertySymbols},wt=it("Reflect","ownKeys")||function(t){var e=gt.f(x(t)),n=mt.f;return n?e.concat(n(t)):e},Ot=function(t,e){for(var n=wt(e),r=E.f,o=P.f,i=0;i<n.length;i++){var u=n[i];w(t,u)||r(t,u,o(e,u))}},jt=/#|\.prototype\./,St=function(t,e){var n=Tt[Rt(t)];return n==xt||n!=Pt&&("function"==typeof e?i(e):!!e)},Rt=St.normalize=function(t){return String(t).replace(jt,".").toLowerCase()},Tt=St.data={},Pt=St.NATIVE="N",xt=St.POLYFILL="P",At=St,Et=P.f,_t=function(t,e){var n,r,i,u,c,f=t.target,a=t.global,s=t.stat;if(n=a?o:s?o[f]||I(f,{}):(o[f]||{}).prototype)for(r in e){if(u=e[r],i=t.noTargetGet?(c=Et(n,r))&&c.value:n[r],!At(a?r:f+(s?".":"#")+r,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;Ot(u,i)}(t.sham||i&&i.sham)&&_(u,"sham",!0),nt(n,r,u,t)}},It=Array.isArray||function(t){return"Array"==p(t)},kt=function(t){return Object(d(t))},Mt=function(t,e,n){var r=g(e);r in t?E.f(t,r,s(0,n)):t[r]=n},Ft=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),Ct=Ft&&!Symbol.sham&&"symbol"==typeof Symbol(),Bt=z("wks"),Lt=o.Symbol,Nt=Ct?Lt:$,qt=function(t){return w(Bt,t)||(Ft&&w(Lt,t)?Bt[t]=Lt[t]:Bt[t]=Nt("Symbol."+t)),Bt[t]},zt=qt("species"),Dt=function(t,e){var n;return It(t)&&("function"!=typeof(n=t.constructor)||n!==Array&&!It(n.prototype)?v(n)&&null===(n=n[zt])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===e?0:e)},Wt=it("navigator","userAgent")||"",$t=o.process,Gt=$t&&$t.versions,Kt=Gt&&Gt.v8;Kt?tt=(Z=Kt.split("."))[0]+Z[1]:Wt&&(!(Z=Wt.match(/Edge\/(\d+)/))||Z[1]>=74)&&(Z=Wt.match(/Chrome\/(\d+)/))&&(tt=Z[1]);var Qt,Vt=tt&&+tt,Xt=qt("species"),Yt=qt("isConcatSpreadable"),Ht=Vt>=51||!i((function(){var t=[];return t[Yt]=!1,t.concat()[0]!==t})),Jt=(Qt="concat",Vt>=51||!i((function(){var t=[];return(t.constructor={})[Xt]=function(){return{foo:1}},1!==t[Qt](Boolean).foo}))),Ut=function(t){if(!v(t))return!1;var e=t[Yt];return void 0!==e?!!e:It(t)};_t({target:"Array",proto:!0,forced:!Ht||!Jt},{concat:function(t){var e,n,r,o,i,u=kt(this),c=Dt(u,0),f=0;for(e=-1,r=arguments.length;e<r;e++)if(Ut(i=-1===e?u:arguments[e])){if(f+(o=st(i.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<o;n++,f++)n in i&&Mt(c,f,i[n])}else{if(f>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Mt(c,f++,i)}return c.length=f,c}});var Zt,te=function(t,e,n){if(function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function")}(t),void 0===e)return t;switch(n){case 0:return function(){return t.call(e)};case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,o){return t.call(e,n,r,o)}}return function(){return t.apply(e,arguments)}},ee=[].push,ne=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=5==t||i;return function(c,f,a,s){for(var l,p,h=kt(c),d=y(h),b=te(f,a,3),v=st(d.length),g=0,m=s||Dt,w=e?m(c,v):n?m(c,0):void 0;v>g;g++)if((u||g in d)&&(p=b(l=d[g],g,h),t))if(e)w[g]=p;else if(p)switch(t){case 3:return!0;case 5:return l;case 6:return g;case 2:ee.call(w,l)}else if(o)return!1;return i?-1:r||o?o:w}},re={forEach:ne(0),map:ne(1),filter:ne(2),some:ne(3),every:ne(4),find:ne(5),findIndex:ne(6)},oe=Object.keys||function(t){return dt(t,bt)},ie=u?Object.defineProperties:function(t,e){x(t);for(var n,r=oe(e),o=r.length,i=0;o>i;)E.f(t,n=r[i++],e[n]);return t},ue=it("document","documentElement"),ce=K("IE_PROTO"),fe=function(){},ae=function(t){return"<script>"+t+"<\/script>"},se=function(){try{Zt=document.domain&&new ActiveXObject("htmlfile")}catch(t){}var t,e;se=Zt?function(t){t.write(ae("")),t.close();var e=t.parentWindow.Object;return t=null,e}(Zt):((e=S("iframe")).style.display="none",ue.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(ae("document.F=Object")),t.close(),t.F);for(var n=bt.length;n--;)delete se.prototype[bt[n]];return se()};Q[ce]=!0;var le=Object.create||function(t,e){var n;return null!==t?(fe.prototype=x(t),n=new fe,fe.prototype=null,n[ce]=t):n=se(),void 0===e?n:ie(n,e)},pe=qt("unscopables"),he=Array.prototype;null==he[pe]&&E.f(he,pe,{configurable:!0,value:le(null)});var ye,de=re.find,be=!0;function ve(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ge(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function me(t){return(me=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function we(t,e){return(we=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function Oe(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function je(t,e,n){return(je="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=me(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(n):o.value}})(t,e,n||t)}"find"in[]&&Array(1).find((function(){be=!1})),_t({target:"Array",proto:!0,forced:be},{find:function(t){return de(this,t,arguments.length>1?arguments[1]:void 0)}}),ye="find",he[pe][ye]=!0;var Se=t.fn.bootstrapTable.utils;t.extend(t.fn.bootstrapTable.defaults,{autoRefresh:!1,autoRefreshInterval:60,autoRefreshSilent:!0,autoRefreshStatus:!0,autoRefreshFunction:null}),t.extend(t.fn.bootstrapTable.defaults.icons,{autoRefresh:{bootstrap3:"glyphicon-time icon-time",materialize:"access_time","bootstrap-table":"icon-clock"}[t.fn.bootstrapTable.theme]||"fa-clock"}),t.extend(t.fn.bootstrapTable.locales,{formatAutoRefresh:function(){return"Auto Refresh"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales),t.BootstrapTable=function(e){function n(){return ve(this,n),Oe(this,me(n).apply(this,arguments))}var r,o,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&we(t,e)}(n,e),r=n,(o=[{key:"init",value:function(){for(var t,e=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];(t=je(me(n.prototype),"init",this)).call.apply(t,[this].concat(o)),this.options.autoRefresh&&this.options.autoRefreshStatus&&(this.options.autoRefreshFunction=setInterval((function(){e.refresh({silent:e.options.autoRefreshSilent})}),1e3*this.options.autoRefreshInterval))}},{key:"initToolbar",value:function(){for(var e,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];if((e=je(me(n.prototype),"initToolbar",this)).call.apply(e,[this].concat(o)),this.options.autoRefresh){var u=this.$toolbar.find(">.columns"),c=u.find(".auto-refresh");c.length||(c=t('\n          <button class="auto-refresh '.concat(this.constants.buttonsClass,"\n          ").concat(this.options.autoRefreshStatus?" ".concat(this.constants.classes.buttonActive):"",'"\n          type="button" title="').concat(this.options.formatAutoRefresh(),'">\n          ').concat(this.options.showButtonIcons?Se.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.autoRefresh):"","\n          ").concat(this.options.showButtonText?this.options.formatAutoRefresh():"","\n          </button>\n        ")).appendTo(u)).on("click",t.proxy(this.toggleAutoRefresh,this))}}},{key:"toggleAutoRefresh",value:function(){var t=this;this.options.autoRefresh&&(this.options.autoRefreshStatus?(clearInterval(this.options.autoRefreshFunction),this.$toolbar.find(">.columns").find(".auto-refresh").removeClass(this.constants.classes.buttonActive)):(this.options.autoRefreshFunction=setInterval((function(){t.refresh({silent:t.options.autoRefreshSilent})}),1e3*this.options.autoRefreshInterval),this.$toolbar.find(">.columns").find(".auto-refresh").addClass(this.constants.classes.buttonActive)),this.options.autoRefreshStatus=!this.options.autoRefreshStatus)}}])&&ge(r.prototype,o),i&&ge(r,i),n}(t.BootstrapTable)}));
