/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.17.1
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t,e){return t(e={exports:{}},e.exports),e.exports}var r=function(t){return t&&t.Math==Math&&t},o=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),c={}.propertyIsEnumerable,f=Object.getOwnPropertyDescriptor,a={f:f&&!c.call({1:2},1)?function(t){var e=f(this,t);return!!e&&e.enumerable}:c},l=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},s={}.toString,p=function(t){return s.call(t).slice(8,-1)},y="".split,h=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?y.call(t,""):Object(t)}:Object,b=function(t){return h(function(t){if(null==t)throw TypeError("Can't call method on "+t);return t}(t))},g=function(t){return"object"==typeof t?null!==t:"function"==typeof t},d=function(t,e){if(!g(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!g(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!g(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!g(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")},v={}.hasOwnProperty,m=function(t,e){return v.call(t,e)},w=o.document,S=g(w)&&g(w.createElement),j=!u&&!i((function(){return 7!=Object.defineProperty((t="div",S?w.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),O=Object.getOwnPropertyDescriptor,A={f:u?O:function(t,e){if(t=b(t),e=d(e,!0),j)try{return O(t,e)}catch(t){}if(m(t,e))return l(!a.f.call(t,e),t[e])}},P=function(t){if(!g(t))throw TypeError(String(t)+" is not an object");return t},T=Object.defineProperty,E={f:u?T:function(t,e,n){if(P(t),e=d(e,!0),P(n),j)try{return T(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},x=u?function(t,e,n){return E.f(t,e,l(1,n))}:function(t,e,n){return t[e]=n,t},M=function(t,e){try{x(o,t,e)}catch(n){o[t]=e}return e},_=o["__core-js_shared__"]||M("__core-js_shared__",{}),z=Function.toString;"function"!=typeof _.inspectSource&&(_.inspectSource=function(t){return z.call(t)});var C,V,k,I,F=_.inspectSource,L=o.WeakMap,N="function"==typeof L&&/native code/.test(F(L)),q=n((function(t){(t.exports=function(t,e){return _[t]||(_[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),$=0,B=Math.random(),D=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++$+B).toString(36)},G=q("keys"),W={},K=o.WeakMap;if(N){var Q=new K,R=Q.get,Y=Q.has,H=Q.set;C=function(t,e){return H.call(Q,t,e),e},V=function(t){return R.call(Q,t)||{}},k=function(t){return Y.call(Q,t)}}else{var J=G[I="state"]||(G[I]=D(I));W[J]=!0,C=function(t,e){return x(t,J,e),e},V=function(t){return m(t,J)?t[J]:{}},k=function(t){return m(t,J)}}var U,X,Z={set:C,get:V,has:k,enforce:function(t){return k(t)?V(t):C(t,{})},getterFor:function(t){return function(e){var n;if(!g(e)||(n=V(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}}},tt=n((function(t){var e=Z.get,n=Z.enforce,r=String(String).split("String");(t.exports=function(t,e,i,u){var c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,a=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof e||m(i,"name")||x(i,"name",e),n(i).source=r.join("string"==typeof e?e:"")),t!==o?(c?!a&&t[e]&&(f=!0):delete t[e],f?t[e]=i:x(t,e,i)):f?t[e]=i:M(e,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||F(this)}))})),et=o,nt=function(t){return"function"==typeof t?t:void 0},rt=function(t,e){return arguments.length<2?nt(et[t])||nt(o[t]):et[t]&&et[t][e]||o[t]&&o[t][e]},ot=Math.ceil,it=Math.floor,ut=function(t){return isNaN(t=+t)?0:(t>0?it:ot)(t)},ct=Math.min,ft=function(t){return t>0?ct(ut(t),9007199254740991):0},at=Math.max,lt=Math.min,st=function(t,e){var n=ut(t);return n<0?at(n+e,0):lt(n,e)},pt=function(t){return function(e,n,r){var o,i=b(e),u=ft(i.length),c=st(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},yt={includes:pt(!0),indexOf:pt(!1)}.indexOf,ht=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,e){var n,r=b(t),o=0,i=[];for(n in r)!m(W,n)&&m(r,n)&&i.push(n);for(;e.length>o;)m(r,n=e[o++])&&(~yt(i,n)||i.push(n));return i}(t,ht)}},gt={f:Object.getOwnPropertySymbols},dt=rt("Reflect","ownKeys")||function(t){var e=bt.f(P(t)),n=gt.f;return n?e.concat(n(t)):e},vt=function(t,e){for(var n=dt(e),r=E.f,o=A.f,i=0;i<n.length;i++){var u=n[i];m(t,u)||r(t,u,o(e,u))}},mt=/#|\.prototype\./,wt=function(t,e){var n=jt[St(t)];return n==At||n!=Ot&&("function"==typeof e?i(e):!!e)},St=wt.normalize=function(t){return String(t).replace(mt,".").toLowerCase()},jt=wt.data={},Ot=wt.NATIVE="N",At=wt.POLYFILL="P",Pt=wt,Tt=A.f,Et=Array.isArray||function(t){return"Array"==p(t)},xt=function(t,e,n){var r=d(e);r in t?E.f(t,r,l(0,n)):t[r]=n},Mt=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),_t=Mt&&!Symbol.sham&&"symbol"==typeof Symbol(),zt=q("wks"),Ct=o.Symbol,Vt=_t?Ct:D,kt=function(t){return m(zt,t)||(Mt&&m(Ct,t)?zt[t]=Ct[t]:zt[t]=Vt("Symbol."+t)),zt[t]},It=rt("navigator","userAgent")||"",Ft=o.process,Lt=Ft&&Ft.versions,Nt=Lt&&Lt.v8;Nt?X=(U=Nt.split("."))[0]+U[1]:It&&(!(U=It.match(/Edge\/(\d+)/))||U[1]>=74)&&(U=It.match(/Chrome\/(\d+)/))&&(X=U[1]);var qt,$t=X&&+X,Bt=kt("species"),Dt=kt("species"),Gt=[].slice,Wt=Math.max;!function(t,e){var n,r,i,u,c,f=t.target,a=t.global,l=t.stat;if(n=a?o:l?o[f]||M(f,{}):(o[f]||{}).prototype)for(r in e){if(u=e[r],i=t.noTargetGet?(c=Tt(n,r))&&c.value:n[r],!Pt(a?r:f+(l?".":"#")+r,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;vt(u,i)}(t.sham||i&&i.sham)&&x(u,"sham",!0),tt(n,r,u,t)}}({target:"Array",proto:!0,forced:(qt="slice",!($t>=51||!i((function(){var t=[];return(t.constructor={})[Bt]=function(){return{foo:1}},1!==t[qt](Boolean).foo}))))},{slice:function(t,e){var n,r,o,i=b(this),u=ft(i.length),c=st(t,u),f=st(void 0===e?u:e,u);if(Et(i)&&("function"!=typeof(n=i.constructor)||n!==Array&&!Et(n.prototype)?g(n)&&null===(n=n[Dt])&&(n=void 0):n=void 0,n===Array||void 0===n))return Gt.call(i,c,f);for(r=new(void 0===n?Array:n)(Wt(f-c,0)),o=0;c<f;c++,o++)c in i&&xt(r,o,i[c]);return r.length=o,r}});var Kt=function(t){return void 0!==t.$el.data("resizableColumns")},Qt=function(t){!t.options.resizable||t.options.cardView||Kt(t)||t.$el.resizableColumns({store:window.store})},Rt=function(t){Kt(t)&&t.$el.data("resizableColumns").destroy()},Yt=function(t){Rt(t),Qt(t)};t.extend(t.fn.bootstrapTable.defaults,{resizable:!1});var Ht=t.fn.bootstrapTable.Constructor,Jt=Ht.prototype.initBody,Ut=Ht.prototype.toggleView,Xt=Ht.prototype.resetView;Ht.prototype.initBody=function(){for(var t=this,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];Jt.apply(this,Array.prototype.slice.apply(n)),t.$el.off("column-switch.bs.table page-change.bs.table").on("column-switch.bs.table page-change.bs.table",(function(){Yt(t)}))},Ht.prototype.toggleView=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];Ut.apply(this,Array.prototype.slice.apply(e)),this.options.resizable&&this.options.cardView&&Rt(this)},Ht.prototype.resetView=function(){for(var t=this,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];Xt.apply(this,Array.prototype.slice.apply(n)),this.options.resizable&&setTimeout((function(){Qt(t)}),100)}}));