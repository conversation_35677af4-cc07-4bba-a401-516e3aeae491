(function($) {
    'use strict';
    
    // 配置常量
    const REGEX_PATTERNS = {
        // 更准确的中国手机号（支持最新号段）
        phone: /^1[3-9]\d{9}$/,
        // 座机号（移除全局标志）
        telephone: /^(0\d{2,3}-)?\d{7,8}$/,
        // 中文姓名（支持少数民族长姓名）
        chineseName: /^[\u4e00-\u9fa5·]{2,8}$/,
        // 用户名（数字字母下划线，更安全）
        username: /^[a-zA-Z0-9_]{2,20}$/,
        // 身份证（15位或18位）
        identity: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$|^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$/,
        // 18位身份证
        identity18: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        // 出生日期
        birthDate: /^(19|20)\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/,
        // IP地址（简化版）
        ipAddress: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    };

    // 错误消息常量
    const ERROR_MESSAGES = {
        phone: "请填写正确的11位手机号码",
        telephone: "请填写正确的座机号码（如：010-12345678）",
        chineseName: "姓名只能包含中文字符，长度2-8位",
        username: "用户名只能包含字母、数字和下划线，长度2-20位",
        identity: "请输入正确的15位或18位身份证号码",
        identity18: "请输入正确的18位身份证号码",
        birthDate: "出生日期格式错误，示例：2000-01-01",
        ipAddress: "IP地址格式错误，示例：***********",
        passwordDifferent: "新密码不能与原密码相同",
        passwordSame: "两次输入的密码必须一致"
    };

    // 工具函数
    const ValidationUtils = {
        // 安全的密码比较
        comparePasswords: function(password1, password2, shouldBeSame) {
            if (typeof password1 !== 'string' || typeof password2 !== 'string') {
                return false;
            }
            return shouldBeSame ? password1 === password2 : password1 !== password2;
        },
        
        // 获取元素值（带缓存）
        getElementValue: function(selector) {
            const $element = $(selector);
            return $element.length > 0 ? $element.val().trim() : '';
        }
    };

    // 设置默认配置
    $.validator.setDefaults({
        errorElement: 'span',
        errorClass: 'help-block error-message',
        highlight: function(element) {
            $(element).closest('.form-group').addClass('has-error');
        },
        unhighlight: function(element) {
            $(element).closest('.form-group').removeClass('has-error');
        },
        submitHandler: function(form) {
            form.submit();
        }
    });

    // 添加自定义验证方法
    const customMethods = {
        // 手机号验证
        isPhone: {
            method: function(value, element) {
                return this.optional(element) || REGEX_PATTERNS.phone.test(value);
            },
            message: ERROR_MESSAGES.phone
        },
        
        // 座机号验证
        isTelephone: {
            method: function(value, element) {
                return this.optional(element) || REGEX_PATTERNS.telephone.test(value);
            },
            message: ERROR_MESSAGES.telephone
        },
        
        // 中文姓名验证
        isChineseName: {
            method: function(value, element) {
                return this.optional(element) || REGEX_PATTERNS.chineseName.test(value);
            },
            message: ERROR_MESSAGES.chineseName
        },
        
        // 用户名验证
        isUsername: {
            method: function(value, element) {
                return this.optional(element) || REGEX_PATTERNS.username.test(value);
            },
            message: ERROR_MESSAGES.username
        },
        
        // 身份证验证
        isIdentity: {
            method: function(value, element) {
                return this.optional(element) || REGEX_PATTERNS.identity.test(value);
            },
            message: ERROR_MESSAGES.identity
        },
        
        // 18位身份证验证
        isIdentity18: {
            method: function(value, element) {
                return this.optional(element) || REGEX_PATTERNS.identity18.test(value);
            },
            message: ERROR_MESSAGES.identity18
        },
        
        // 出生日期验证
        isBirthDate: {
            method: function(value, element) {
                if (this.optional(element)) return true;
                if (!REGEX_PATTERNS.birthDate.test(value)) return false;
                
                // 额外验证：检查日期是否有效且不超过当前日期
                const date = new Date(value);
                const now = new Date();
                return date <= now && date.getFullYear() >= 1900;
            },
            message: ERROR_MESSAGES.birthDate
        },
        
        // IP地址验证
        isIpAddress: {
            method: function(value, element) {
                return this.optional(element) || REGEX_PATTERNS.ipAddress.test(value);
            },
            message: ERROR_MESSAGES.ipAddress
        },
        
        // 改进的密码不同验证
        isPasswordDifferent: {
            method: function(value, element, param) {
                const oldPasswordSelector = param || '#pwdOld';
                const oldPassword = ValidationUtils.getElementValue(oldPasswordSelector);
                return ValidationUtils.comparePasswords(oldPassword, value, false);
            },
            message: ERROR_MESSAGES.passwordDifferent
        },
        
        // 改进的密码相同验证
        isPasswordSame: {
            method: function(value, element, param) {
                const passwordSelector = param || '#pwdNew';
                const password = ValidationUtils.getElementValue(passwordSelector);
                return ValidationUtils.comparePasswords(password, value, true);
            },
            message: ERROR_MESSAGES.passwordSame
        },
        
        // 通用的不等于验证
        notEqual: {
            method: function(value, element, param) {
                return value !== param;
            },
            message: function(param) {
                return "输入值不能为 " + param;
            }
        },
        
        // 通用的大于验证
        greaterThan: {
            method: function(value, element, param) {
                return this.optional(element) || parseFloat(value) > parseFloat(param);
            },
            message: function(param) {
                return "输入值必须大于 " + param;
            }
        }
    };

    // 注册所有自定义验证方法
    Object.keys(customMethods).forEach(function(methodName) {
        const method = customMethods[methodName];
        $.validator.addMethod(methodName, method.method, method.message);
    });

    // DOM 加载完成后初始化表单验证
    $(document).ready(function() {
        // 基础信息表单验证配置
        const basicInfoFormConfig = {
            rules: {
                name: {
                    required: true,
                    isChineseName: true
                },
                sex: "required",
                birth: {
                    required: true,
                    isBirthDate: true
                },
                mobile: {
                    required: true,
                    isPhone: true
                },
                email: {
                    required: true,
                    email: true
                }
            },
            messages: {
                name: {
                    required: "请输入姓名"
                },
                sex: {
                    required: "请选择性别"
                },
                birth: {
                    required: "请选择出生日期"
                },
                mobile: {
                    required: "请输入手机号码"
                },
                email: {
                    required: "请输入邮箱地址",
                    email: "请输入正确的邮箱格式"
                }
            },
            errorPlacement: function(error, element) {
                element.next('.error-message').remove();
                element.closest('.form-group').append(error);
            },
            success: function(label) {
                label.closest('.form-group').removeClass('has-error').addClass('has-success');
                label.remove();
            },
            submitHandler: function(form) {
                // 这里可以添加 AJAX 提交逻辑
                console.log('表单验证通过，准备提交');
                // form.submit(); // 根据需要启用
            }
        };

        // 修改密码表单验证配置
        const modifyPasswordFormConfig = {
            rules: {
                pwdOld: {
                    required: true,
                    minlength: 6
                },
                pwdNew: {
                    required: true,
                    minlength: 6,
                    isPasswordDifferent: '#pwdOld'
                },
                confirmPassword: {
                    required: true,
                    minlength: 6,
                    isPasswordSame: '#pwdNew'
                }
            },
            messages: {
                pwdOld: {
                    required: '请输入原密码',
                    minlength: '密码长度至少6位'
                },
                pwdNew: {
                    required: '请输入新密码',
                    minlength: '密码长度至少6位'
                },
                confirmPassword: {
                    required: '请确认新密码',
                    minlength: '密码长度至少6位'
                }
            },
            errorPlacement: function(error, element) {
                element.closest('.form-group').append(error);
            }
        };

        // 应用验证配置
        $('#basicInfoForm').validate(basicInfoFormConfig);
        $('#modifyPwd').validate(modifyPasswordFormConfig);
    });

})(jQuery);
