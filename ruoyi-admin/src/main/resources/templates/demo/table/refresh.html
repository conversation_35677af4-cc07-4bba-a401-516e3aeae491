<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:insert="~{include :: header('表格自动刷新')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <!-- data-auto-refresh设置true为启用自动刷新插件
                 data-auto-refresh-interval为每次自动刷新发生的时间（以秒为单位）默认60。
                 data-auto-refresh-silent设置为true可以自动无提示刷新。默认： true
                 data-auto-refresh-status 设置true为启用自动刷新。这是表加载时自动刷新的状态。单击按钮切换此属性。这只是自动刷新的默认状态，因为用户始终可以通过单击按钮来更改它。 默认： true  -->
            <table id="bootstrap-table" data-auto-refresh="true" data-auto-refresh-interval="30"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:insert="~{include :: bootstrap-table-auto-refresh-js}"/>
<script th:inline="javascript">
    var prefix = ctx + "demo/table";

    $(function () {
        var options = {
            url: prefix + "/list",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [{
                checkbox: true
            },
                {
                    field: 'userId',
                    title: '用户ID'
                },
                {
                    field: 'userCode',
                    title: '用户编号'
                },
                {
                    field: 'userName',
                    title: '用户姓名'
                },
                {
                    field: 'userPhone',
                    title: '用户手机'
                },
                {
                    field: 'userEmail',
                    title: '用户邮箱'
                },
                {
                    field: 'userBalance',
                    title: '用户余额'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:;" onclick="remove(this)"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    // 假删除操作
    function remove(obj) {
        $.modal.confirm("确认要删除吗？", function () {
            $(obj).parents("tr").remove();
            $.modal.msgSuccess('已删除!');
        });
    }
</script>
</body>
</html>
