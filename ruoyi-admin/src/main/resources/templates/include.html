<!-- 通用CSS -->
<head th:fragment=header(title)>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <title th:text="${title}"></title>
    <link rel="shortcut icon" href="favicon.ico">
    <link th:href="@{/css/bootstrap.min.css?v=3.3.7}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css?v=4.7.0}" rel="stylesheet"/>
    <!-- bootstrap-table 表格插件样式 -->
    <link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css?v=1.18.3}" rel="stylesheet"/>
    <link th:href="@{/css/animate.min.css?v=20210831}" rel="stylesheet"/>
    <link th:href="@{/css/style.min.css?v=20210831}" rel="stylesheet"/>
    <link th:href="@{/ruoyi/css/ry-ui.css?v=4.7.7}" rel="stylesheet"/>
</head>

<!-- 通用JS -->
<div th:fragment="footer">
    <script th:inline="javascript"> var ctx = [[@{/}]]; var lockscreen = [[${session.lockscreen}]]; if(lockscreen){window.top.location=ctx+"lockscreen";} </script>
    <a id="scroll-up" href="#" class="btn btn-sm display"><i class="fa fa-angle-double-up"></i></a>
    <script th:src="@{/js/jquery.min.js?v=3.6.3}"></script>
    <script th:src="@{/js/bootstrap.min.js?v=3.3.7}"></script>
    <!-- bootstrap-table 表格插件 -->
    <script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table.min.js?v=1.18.3}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/locale/bootstrap-table-zh-CN.min.js?v=1.18.3}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/mobile/bootstrap-table-mobile.js?v=1.18.3}"></script>
    <!-- jquery-validate 表单验证插件 -->
    <script th:src="@{/ajax/libs/validate/jquery.validate.min.js?v=1.19.3}"></script>
    <script th:src="@{/ajax/libs/validate/jquery.validate.extend.js?v=1.19.3}"></script>
    <script th:src="@{/ajax/libs/validate/messages_zh.js?v=1.19.3}"></script>
    <!-- bootstrap-table 表格树插件 -->
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/tree/bootstrap-table-tree.min.js?v=1.18.3}"></script>
    <!-- 遮罩层 -->
    <script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js?v=2.70.0}"></script>
    <script th:src="@{/ajax/libs/iCheck/icheck.min.js?v=1.0.3}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js?v=3.5.1}"></script>
    <script th:src="@{/ajax/libs/layui/layui.min.js?v=2.7.5}"></script>
    <script th:src="@{/ruoyi/js/common.js?v=4.7.7}"></script>
    <script th:src="@{/ruoyi/js/ry-ui.js?v=4.7.7}"></script>
</div>

<!-- ztree树插件 -->
<div th:fragment="ztree-css">
	<link th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}" rel="stylesheet"/>
</div>
<div th:fragment="ztree-js">
	<script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
</div>

<!-- select2下拉框插件 -->
<div th:fragment="select2-css">
    <link th:href="@{/ajax/libs/select2/select2.min.css?v=4.0.13}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/select2/select2-bootstrap.min.css?v=4.0.13}" rel="stylesheet"/>
</div>
<div th:fragment="select2-js">
    <script th:src="@{/ajax/libs/select2/select2.min.js?v=4.0.13}"></script>
</div>
<!-- bootstrap-select下拉框插件 -->
<div th:fragment="bootstrap-select-css">
    <link th:href="@{/ajax/libs/bootstrap-select/bootstrap-select.min.css?v=1.13.18}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-select-js">
    <script th:src="@{/ajax/libs/bootstrap-select/bootstrap-select.min.js?v=1.13.18}"></script>
</div>

<!-- datetimepicker日期和时间插件 -->
<div th:fragment="datetimepicker-css">
    <link th:href="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.css?v=2.4.4}" rel="stylesheet"/>
</div>
<div th:fragment="datetimepicker-js">
    <script th:src="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.js?v=2.4.4}"></script>
</div>

<!-- ui布局插件 -->
<div th:fragment="layout-latest-css">
    <link th:href="@{/ajax/libs/jquery-layout/jquery.layout-latest.css?v=1.4.4}" rel="stylesheet"/>
</div>
<div th:fragment="layout-latest-js">
    <script th:src="@{/ajax/libs/jquery-layout/jquery.layout-latest.js?v=1.4.4}"></script>
</div>

<!-- summernote富文本编辑器插件 -->
<div th:fragment="summernote-css">
    <link th:href="@{/ajax/libs/summernote/summernote.min.css?v=0.8.20}" rel="stylesheet"/>
</div>
<div th:fragment="summernote-js">
    <script th:src="@{/ajax/libs/summernote/summernote.min.js?v=0.8.20}"></script>
    <script th:src="@{/ajax/libs/summernote/summernote-zh-CN.min.js?v=0.8.20}"></script>
</div>

<!-- cropper图像裁剪插件 -->
<div th:fragment="cropper-css">
    <link th:href="@{/ajax/libs/cropper/cropper.min.css?v=1.5.12}" rel="stylesheet"/>
</div>
<div th:fragment="cropper-js">
    <script th:src="@{/ajax/libs/cropper/cropper.min.js?v=1.5.12}"></script>
</div>

<!-- jasny功能扩展插件 -->
<div th:fragment="jasny-bootstrap-css">
    <link th:href="@{/ajax/libs/jasny/jasny-bootstrap.min.css?v=3.1.3}" rel="stylesheet"/>
</div>
<div th:fragment="jasny-bootstrap-js">
    <script th:src="@{/ajax/libs/jasny/jasny-bootstrap.min.js?v=3.1.3}"></script>
</div>

<!-- fileinput文件上传插件 -->
<div th:fragment="bootstrap-fileinput-css">
    <link th:href="@{/ajax/libs/bootstrap-fileinput/fileinput.min.css?v=5.5.2}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-fileinput-js">
    <script th:src="@{/ajax/libs/bootstrap-fileinput/fileinput.min.js?v=5.5.2}"></script>
</div>

<!-- duallistbox双列表框插件 -->
<div th:fragment="bootstrap-duallistbox-css">
    <link th:href="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.css?v=3.0.9}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-duallistbox-js">
    <script th:src="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.js?v=3.0.9}"></script>
</div>

<!-- suggest搜索自动补全 -->
<div th:fragment="bootstrap-suggest-js">
    <script th:src="@{/ajax/libs/suggest/bootstrap-suggest.min.js?v=0.1.29}"></script>
</div>

<!-- typeahead搜索自动补全 -->
<div th:fragment="bootstrap-typeahead-js">
    <script th:src="@{/ajax/libs/typeahead/bootstrap-typeahead.min.js?v=4.0.2}"></script>
</div>

<!-- 多级联动下拉 -->
<div th:fragment="jquery-cxselect-js">
    <script th:src="@{/ajax/libs/cxselect/jquery.cxselect.min.js?v=1.4.2}"></script>
</div>

<!-- jsonview格式化和语法高亮JSON格式数据查看插件 -->
<div th:fragment="jsonview-css">
    <link th:href="@{/ajax/libs/jsonview/jquery.jsonview.css?v=1.2.0}" rel="stylesheet"/>
</div>
<div th:fragment="jsonview-js">
    <script th:src="@{/ajax/libs/jsonview/jquery.jsonview.js?v=1.2.0}"></script>
</div>

<!-- jquery.smartwizard表单向导插件 -->
<div th:fragment="jquery-smartwizard-css">
    <link th:href="@{/ajax/libs/smartwizard/smart_wizard_all.min.css?v=5.1.1}" rel="stylesheet"/>
</div>
<div th:fragment="jquery-smartwizard-js">
    <script th:src="@{/ajax/libs/smartwizard/jquery.smartWizard.min.js?v=5.1.1}"></script>
</div>

<!-- ECharts百度统计图表插件 -->
<div th:fragment="echarts-js">
    <script th:src="@{/ajax/libs/report/echarts/echarts-all.min.js?v=4.2.1}"></script>
</div>

<!-- peity图表组合插件 -->
<div th:fragment="peity-js">
    <script th:src="@{/ajax/libs/report/peity/jquery.peity.min.js?v=2.0.3}"></script>
</div>

<!-- sparkline线状图插件 -->
<div th:fragment="sparkline-js">
    <script th:src="@{/ajax/libs/report/sparkline/jquery.sparkline.min.js?v=2.1.2}"></script>
</div>

<!-- 表格行拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-rows-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-rows/bootstrap-table-reorder-rows.js?v=1.18.3}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-rows/jquery.tablednd.js?v=1.0.3}"></script>
</div>

<!-- 表格列拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-columns-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/jquery.dragtable.js?v=5.3.5}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/bootstrap-table-reorder-columns.js?v=1.18.3}"></script>
</div>

<!-- 表格列宽拖动插件 -->
<div th:fragment="bootstrap-table-resizable-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/jquery.resizableColumns.min.js?v=0.1.0}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/bootstrap-table-resizable.js?v=1.18.3}"></script>
</div>

<!-- 表格行内编辑插件 -->
<div th:fragment="bootstrap-editable-css">
    <link th:href="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.css?v=1.5.1}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-table-editable-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.min.js?v=1.5.1}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-table-editable.js?v=1.18.3}"></script>
</div>

<!-- 表格导出插件 -->
<div th:fragment="bootstrap-table-export-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/export/bootstrap-table-export.js?v=1.18.3}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/export/tableExport.min.js?v=1.10.24}"></script>
</div>

<!-- 表格冻结列插件 -->
<div th:fragment="bootstrap-table-fixed-columns-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/columns/bootstrap-table-fixed-columns.js?v=1.18.3}"></script>
</div>

<!-- 表格自动刷新插件 -->
<div th:fragment="bootstrap-table-auto-refresh-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/auto-refresh/bootstrap-table-auto-refresh.js?v=1.18.3}"></script>
</div>

<!-- 表格打印插件 -->
<div th:fragment="bootstrap-table-print-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/print/bootstrap-table-print.js?v=1.18.3}"></script>
</div>

<!-- 表格视图分页插件 -->
<div th:fragment="bootstrap-table-custom-view-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/custom-view/bootstrap-table-custom-view.js?v=1.18.3}"></script>
</div>
