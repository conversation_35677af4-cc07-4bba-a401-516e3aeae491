<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('用户分配角色')}"/>
</head>
<body>
<div class="main-content">
    <form id="form-user-add" class="form-horizontal">
        <input type="hidden" id="userId" name="userId" th:value="${user.userId}">
        <h4 class="form-header h4">基本信息</h4>
        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">用户名称：</label>
                    <div class="col-sm-8">
                        <input name="userName" class="form-control" type="text" disabled th:value="${user.userName}">
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">登录账号：</label>
                    <div class="col-sm-8">
                        <input name="loginName" class="form-control" type="text" disabled th:value="${user.loginName}">
                    </div>
                </div>
            </div>
        </div>

        <h4 class="form-header h4">分配角色</h4>
        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var prefix = ctx + "system/user/authRole";
    var roles = [[${roles}]]

    $(function () {
        var options = {
            data: roles,
            sidePagination: "client",
            sortName: "roleSort",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect: true,
            maintainSelected: true,
            columns: [{
                checkbox: true,
                formatter: function (value, row, index) {
                    if ($.common.isEmpty(value)) {
                        return {checked: row.flag};
                    } else {
                        return {checked: value}
                    }
                }
            },
                {
                    field: 'roleId',
                    title: '角色编号'
                },
                {
                    field: 'roleSort',
                    title: '排序',
                    sortable: true,
                    visible: false
                },
                {
                    field: 'roleName',
                    title: '角色名称'
                },
                {
                    field: 'roleKey',
                    title: '权限字符',
                    sortable: true
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                }]
        };
        $.table.init(options);
    });

    /* 添加角色-提交 */
    function submitHandler(index, layero) {
        var roleIds = [];
        var data = $('#bootstrap-table').bootstrapTable('getData');
        for (var i = 0; i < data.length; i++) {
            if (data[i][0] || ($.common.isEmpty(data[i][0]) && data[i].flag)) {
                roleIds.push(data[i].roleId)
            }
        }
        $.operate.saveTab(prefix + "/insertAuthRole", {"userId": $("#userId").val(), "roleIds": roleIds.join()});
    }
</script>
</body>
</html>
