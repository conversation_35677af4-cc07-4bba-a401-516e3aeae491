maMip:
  # 环境标识，test=测试环境，prod=正式环境
  env: prod
  # 合作方ID，由腾讯工程师分配
  partnerId: 50004335
  # 合作方密钥，由腾讯工程师分配
  # 测试
  # partnerKey: d062af257bbe6daae418e605ef2b6ee6
  # 正式
  partnerKey: 77e6d70b76dd5028a53200d30e716144
  # 渠道号，由腾讯工程师分配
  channelNo: AAFpLq7W-hPgZEn0ftOT2528
  # 城市编码，参见《对接移动医疗平台接口文档_国家局》（https://docs.qq.com/doc/DV3lxV3hSbXFudVBE）
  cityId: 650100
  # 定点医药机构编码，参见《测试环境反馈单》
  orgCode: H65010300094
  # 医保支付密钥
  mipKey: 735c5b44a91bd164ccf735f6b45bf9cc
  # 定点医药机构小程序/H5应用ID
  orgAppId: "1IUVA1THU04E8D430B0A00001A2059A2"
  # 机构渠道编码，参见国家医保局医保移动支付反馈单
  orgChannelCode: "BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxt29ZVU1XoA6qG4nF3dLRn3"
  # 公众号或小程序的appId
  appId: wxccb0430de6991c6d

mpMip:
  # 环境标识，test=测试环境，prod=正式环境
  env: test
  # 合作方ID，由腾讯工程师分配
  partnerId: ""
  # 合作方密钥，由腾讯工程师分配
  partnerKey: ""
  # 渠道号，由腾讯工程师分配
  channelNo: ""
  # 城市编码，参见《对接移动医疗平台接口文档_国家局》（https://docs.qq.com/doc/DV3lxV3hSbXFudVBE）
  cityId: 650100
  # 定点医药机构编码，参见《测试环境反馈单》
  orgCode: H65010300094
  # 医保支付密钥
  mipKey: ""
  # 定点医药机构小程序/H5应用ID
  orgAppId: ""
  # 机构渠道编码，参见国家医保局医保移动支付反馈单
  orgChannelCode: ""
  # 公众号或小程序的appId
  appId: ""