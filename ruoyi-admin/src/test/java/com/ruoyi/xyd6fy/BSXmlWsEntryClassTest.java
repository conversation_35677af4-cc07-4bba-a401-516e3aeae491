package com.ruoyi.xyd6fy;

import org.junit.jupiter.api.Test;
import org.mospital.jackson.DateTimeFormatters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import space.lzhq.ph.domain.Reservation;
import space.lzhq.ph.service.IReservationService;
import space.lzhq.ph.webservice.BSXmlWsEntryClass;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ActiveProfiles({"test", "druid"})
@SpringBootTest
class BSXmlWsEntryClassTest {

    @Autowired
    private BSXmlWsEntryClass bsXmlWsEntryClass;

    @Autowired
    private IReservationService reservationService;

    private final String urid = "YQTL";
    private final String password = "ysrNyK62ton7b4PN";

    private final Long reservationId = 194311L;

    @Test
    void testInvoke_OutAppointSend_Stop() {
        final String service = "OutAppointSend_Stop";

        Reservation reservation = reservationService.getById(reservationId);
        assertNotNull(reservation);

        String resultXml = bsXmlWsEntryClass.invoke(service, urid, password, buildParameter(reservation));
        assertTrue(resultXml.contains("<Status>true</Status>"));
        assertTrue(resultXml.contains("<Detail>成功</Detail>"));
    }

    private String buildParameter(Reservation reservation) {
        return """
                <BSXml>
                    <MsgHeader>
                        <Sender>HIS</Sender>
                        <AuthorOrganization DisplayName="新疆儿童医院">45760785-7</AuthorOrganization>
                        <MsgType>OutAppointSend</MsgType>
                        <OperationType>Update</OperationType>
                        <MsgVersion>3.1</MsgVersion>
                    </MsgHeader>
                    <Visit>
                        <ScheduleMark>%s</ScheduleMark>
                        <AppointmentId>%s</AppointmentId>
                        <SourcePatientId>%s</SourcePatientId>
                        <RegisteredDateTime>%s</RegisteredDateTime>
                        <AppointmentDateTime>%s</AppointmentDateTime>
                        <IdCard>%s</IdCard>
                        <Name>%s</Name>
                        <RegisteredDept>%s</RegisteredDept>
                        <RegisteredDeptNane>%s</RegisteredDeptNane>
                        <RegisteredDcotor>%s</RegisteredDcotor>
                        <RegisteredDcotorName>%s</RegisteredDcotorName>
                        <OperatingName>Test</OperatingName>
                        <OperatingDateTime>%s</OperatingDateTime>
                        <messageContent>您预约的科室已停诊</messageContent>
                    </Visit>
                </BSXml>
                """.formatted(
                reservation.getId(),
                reservation.getReservationNumber(),
                reservation.getPatientId(),
                reservation.getOperationTime().format(DateTimeFormatters.INSTANCE.getNORM_DATETIME_FORMATTER()),
                reservation.getJzTime().format(DateTimeFormatters.INSTANCE.getNORM_DATETIME_FORMATTER()),
                reservation.getIdCardNo(),
                reservation.getName(),
                reservation.getDepartmentCode(),
                reservation.getDepartmentName(),
                reservation.getDoctorCode(),
                reservation.getDoctorName(),
                LocalDateTime.now().format(DateTimeFormatters.INSTANCE.getNORM_DATETIME_FORMATTER())
        );
    }

}
