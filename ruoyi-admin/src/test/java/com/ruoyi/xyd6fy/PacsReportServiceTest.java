package com.ruoyi.xyd6fy;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import space.lzhq.ph.service.IPacsReportService;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@Disabled
@ActiveProfiles({"test", "druid"})
@SpringBootTest
class PacsReportServiceTest {

    @Autowired
    private IPacsReportService pacsReportService;

    @Test
    void testSelectReportLinksByApplicationNo() {
        String applicationNo = "822738";
        List<String> reportLinks = pacsReportService.selectReportLinksByApplicationNo(applicationNo);
        assertThat(reportLinks).containsExactly(
                "https://xjetyy-pacs-pdf.xjyqtl.cn/imgreport/2025/0604/1F040E45E01C6CAF9E8155E8ED7C6B7E/20250604_094341_213.pdf",
                "https://xjetyy-pacs-pdf.xjyqtl.cn/imgreport/2025/0604/1F040E46D6636AB59E8155E8ED7C6B7E/20250604_094755_555.pdf"
        );
    }

    @Test
    void testSelectReportsByApplicationNos() {
        List<String> applicationNos = List.of("822738", "813155");
        Map<String, List<String>> reports = pacsReportService.selectReportsByApplicationNos(applicationNos);
        assertThat(reports)
                .hasSize(2)
                .containsEntry("822738", List.of(
                        "https://xjetyy-pacs-pdf.xjyqtl.cn/imgreport/2025/0604/1F040E45E01C6CAF9E8155E8ED7C6B7E/20250604_094341_213.pdf",
                        "https://xjetyy-pacs-pdf.xjyqtl.cn/imgreport/2025/0604/1F040E46D6636AB59E8155E8ED7C6B7E/20250604_094755_555.pdf"
                ))
                .containsEntry("813155", List.of(
                        "https://xjetyy-pacs-pdf.xjyqtl.cn/imgreport/2025/0526/1F03552D08B46274B6FA73CF58590365/20250526_172931_527.pdf"
                ));
    }

}
