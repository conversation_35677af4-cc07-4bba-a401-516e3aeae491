package com.ruoyi.framework.controller;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.HandlerMapping;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件下载控制器
 *
 * <AUTHOR>
 */
@Controller
public class FileDownloadController {

    private static final Logger log = LoggerFactory.getLogger(FileDownloadController.class);

    @GetMapping(Constants.RESOURCE_PREFIX + "/**")
    public ResponseEntity<Resource> downloadFile(HttpServletRequest request) {
        try {
            // Use Spring MVC's recommended way to safely extract the path variable to prevent path manipulation.
            final String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
            final String bestMatchingPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
            String filePath = new org.springframework.util.AntPathMatcher().extractPathWithinPattern(bestMatchingPattern, path);

            // 构建完整的文件路径
            Path fullPath = Paths.get(RuoYiConfig.getProfile(), filePath);
            File file = fullPath.toFile();

            // 检查文件是否存在
            if (!file.exists() || !file.isFile()) {
                return ResponseEntity.notFound().build();
            }

            // 安全检查：确保文件在允许的目录内
            String canonicalFilePath = file.getCanonicalPath();
            String canonicalBasePath = new File(RuoYiConfig.getProfile()).getCanonicalPath();
            if (!canonicalFilePath.startsWith(canonicalBasePath)) {
                return ResponseEntity.notFound().build();
            }

            // 创建资源
            Resource resource = new FileSystemResource(file);

            // 设置Content-Disposition头，强制下载
            ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                    .filename(file.getName(), StandardCharsets.UTF_8)
                    .build();

            // 尝试检测文件类型
            String contentType = Files.probeContentType(fullPath);
            if (contentType == null) {
                contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE;
            }

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
                    .contentType(MediaType.parseMediaType(contentType))
                    .body(resource);

        } catch (Exception e) {
            log.error("File download failed: {}", request.getRequestURI(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

}
