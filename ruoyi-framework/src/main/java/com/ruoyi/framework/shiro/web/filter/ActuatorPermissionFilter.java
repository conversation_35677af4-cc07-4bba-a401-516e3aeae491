package com.ruoyi.framework.shiro.web.filter;

import com.ruoyi.common.core.domain.AjaxResult;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.mospital.jackson.JacksonKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.AntPathMatcher;

import java.io.IOException;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Actuator 端点权限过滤器
 * 为不同的 Actuator 端点提供基于权限字符串的访问控制
 *
 * <AUTHOR>
 */
public class ActuatorPermissionFilter extends AccessControlFilter {

    private static final Logger log = LoggerFactory.getLogger(ActuatorPermissionFilter.class);

    /**
     * 路径匹配器，用于支持通配符路径匹配
     */
    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * Actuator 端点权限映射
     */
    private static final Map<String, String> ACTUATOR_PERMISSIONS;

    static {
        // 使用 LinkedHashMap 保证迭代顺序，将更具体的路径模式放在前面
        LinkedHashMap<String, String> actuatorPermissions = new LinkedHashMap<>();
        // 带参数的具体路径模式优先匹配（避免被基础路径覆盖）
        actuatorPermissions.put("/actuator/metrics/**", "monitor:actuator:metrics");
        actuatorPermissions.put("/actuator/env/**", "monitor:actuator:env");
        // 精确匹配的路径
        actuatorPermissions.put("/actuator/health", "monitor:actuator:health");
        actuatorPermissions.put("/actuator/metrics", "monitor:actuator:metrics");
        actuatorPermissions.put("/actuator/env", "monitor:actuator:env");
        actuatorPermissions.put("/actuator/beans", "monitor:actuator:beans");
        actuatorPermissions.put("/actuator/configprops", "monitor:actuator:configprops");
        actuatorPermissions.put("/actuator/mappings", "monitor:actuator:mappings");
        actuatorPermissions.put("/actuator/conditions", "monitor:actuator:view");
        actuatorPermissions.put("/actuator/shutdown", "monitor:actuator:shutdown");
        actuatorPermissions.put("/actuator/threaddump", "monitor:actuator:threaddump");
        actuatorPermissions.put("/actuator/heapdump", "monitor:actuator:heapdump");
        ACTUATOR_PERMISSIONS = Collections.unmodifiableMap(actuatorPermissions);
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception {
        Subject subject = SecurityUtils.getSubject();

        // 检查用户是否已认证
        if (!subject.isAuthenticated()) {
            return false;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String requestURI = httpRequest.getRequestURI();

        // 获取对应的权限字符串
        String requiredPermission = getRequiredPermission(requestURI);

        if (requiredPermission == null) {
            // 未定义的端点，使用通用查看权限
            requiredPermission = "monitor:actuator:view";
        }

        // 检查权限
        return subject.isPermitted(requiredPermission);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 记录访问被拒绝的日志
        String requestURI = httpRequest.getRequestURI();
        String username = SecurityUtils.getSubject().getPrincipal() != null ?
                SecurityUtils.getSubject().getPrincipal().toString() : "anonymous";
        log.warn("Actuator端点访问被拒绝 - 用户: {}, 请求URI: {}", username, requestURI);

        httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
        httpResponse.setContentType("application/json;charset=UTF-8");

        try {
            AjaxResult errorResult = AjaxResult.error("访问被拒绝：权限不足");
            errorResult.put("code", 403);
            String jsonResponse = JacksonKit.INSTANCE.writeValueAsString(errorResult);
            httpResponse.getWriter().write(jsonResponse);
        } catch (IOException e) {
            // 记录写入响应时的异常
            log.error("写入Actuator权限拒绝响应时发生异常 - 用户: {}, 请求URI: {}", username, requestURI, e);
        } catch (Exception e) {
            // 记录JSON序列化异常
            log.error("序列化Actuator权限拒绝响应时发生异常 - 用户: {}, 请求URI: {}", username, requestURI, e);
            // 降级处理：使用简单的JSON字符串
            try {
                httpResponse.getWriter().write("{\"code\":403,\"msg\":\"访问被拒绝：权限不足\"}");
            } catch (IOException ioException) {
                log.error("降级处理写入响应时也发生异常 - 用户: {}, 请求URI: {}", username, requestURI, ioException);
            }
        }

        return false;
    }

    /**
     * 根据请求 URI 获取所需权限
     * 使用 AntPathMatcher 进行路径匹配，支持通配符模式
     */
    private String getRequiredPermission(String requestURI) {
        // 遍历权限映射，使用 AntPathMatcher 进行匹配
        // LinkedHashMap 保证了迭代顺序，更具体的模式会优先匹配
        for (Map.Entry<String, String> entry : ACTUATOR_PERMISSIONS.entrySet()) {
            String pattern = entry.getKey();
            if (pathMatcher.match(pattern, requestURI)) {
                log.debug("请求URI [{}] 匹配到权限模式 [{}]，所需权限: [{}]",
                        requestURI, pattern, entry.getValue());
                return entry.getValue();
            }
        }

        log.debug("请求URI [{}] 未匹配到任何已定义的权限模式", requestURI);
        return null;
    }
}
