package com.ruoyi.quartz.util;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 定时任务执行上下文工具类
 * 用于控制任务执行的环境条件
 *
 * <AUTHOR>
 */
@Component
public class JobExecutionGuard {

    /**
     * JobDataMap中手动执行标记的键名
     */
    public static final String MANUAL_EXECUTION_KEY = "MANUAL_EXECUTION";

    private final Environment environment;

    public JobExecutionGuard(Environment environment) {
        this.environment = environment;
    }

    /**
     * 检查是否允许执行定时任务
     * 只有在生产环境或手动执行时才允许执行
     *
     * @param isManualExecution 是否为手动执行（从JobDataMap中获取）
     * @return true-允许执行，false-不允许执行
     */
    public boolean isExecutionAllowed(boolean isManualExecution) {
        return isManualExecution || isProductionEnvironment();
    }

    /**
     * 获取当前激活的环境配置
     *
     * @return 环境配置数组
     */
    public String[] getActiveProfiles() {
        return environment.getActiveProfiles();
    }

    /**
     * 检查是否为生产环境
     *
     * @return true-生产环境，false-非生产环境
     */
    public boolean isProductionEnvironment() {
        return Arrays.asList(environment.getActiveProfiles()).contains("prod");
    }
}
