package space.lzhq.ph.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class WxPayCallbackUrl {

    @Value("${app.baseUrl}")
    private String baseUrl;

    public String getOnWxPayUrl() {
        return baseUrl + "/open/wx/onWXPay";
    }

    public String getOnWxRefundUrl() {
        return baseUrl + "/open/wx/onWXRefund";
    }

    public String getOnWxTransferUrl() {
        return baseUrl + "/open/wx/onWxTransfer";
    }

    public String getOnWxMipPayUrl() {
        return baseUrl + "/open/wx/onWxInsurancePay";
    }
}
