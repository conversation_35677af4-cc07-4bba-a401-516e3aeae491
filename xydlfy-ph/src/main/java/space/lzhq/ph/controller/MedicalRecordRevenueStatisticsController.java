package space.lzhq.ph.controller;

import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import space.lzhq.ph.domain.MedicalRecordRevenueStatistics;
import space.lzhq.ph.service.IMedicalRecordRevenueStatisticsService;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 病历邮寄业务收入统计Controller
 */
@RestController
@RequestMapping("/ph/medical-record-revenue-statistics")
public class MedicalRecordRevenueStatisticsController extends BaseController {

    private static final String PREFIX = "ph/medicalRecordRevenueStatistics";

    private final IMedicalRecordRevenueStatisticsService revenueStatisticsService;

    @Autowired
    public MedicalRecordRevenueStatisticsController(IMedicalRecordRevenueStatisticsService revenueStatisticsService) {
        this.revenueStatisticsService = revenueStatisticsService;
    }

    /**
     * 病历邮寄收入统计管理主页面
     */
    @RequiresPermissions("ph:medical-record-revenue-statistics:view")
    @GetMapping()
    public ModelAndView revenueStatistics() {
        return new ModelAndView(PREFIX + "/revenue-statistics");
    }

    /**
     * 分页查询收入统计数据
     */
    @RequiresPermissions("ph:medical-record-revenue-statistics:list")
    @PostMapping("/list")
    public TableDataInfo list(
            @RequestParam(value = "startDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(value = "endDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        // 启动分页
        startPage();

        // 执行查询
        List<MedicalRecordRevenueStatistics> list = revenueStatisticsService.selectListByDateRange(startDate, endDate);

        return getDataTable(list);
    }

    /**
     * 导出收入统计数据到Excel
     */
    @RequiresPermissions("ph:medical-record-revenue-statistics:export")
    @Log(title = "病历邮寄收入统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(
            @RequestParam(value = "startDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(value = "endDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletResponse response) {
        PageMethod.orderBy("transaction_date asc"); // 提供默认的排序
        startOrderBy(); // 用户自定义的排序

        List<MedicalRecordRevenueStatistics> list = revenueStatisticsService.selectListByDateRange(startDate, endDate);
        ExcelUtil<MedicalRecordRevenueStatistics> excelUtil = new ExcelUtil<>(MedicalRecordRevenueStatistics.class);
        return excelUtil.exportFastExcel(list, "病历邮寄收入统计");
    }

    /**
     * 手动统计并保存指定日期的收入数据
     */
    @RequiresPermissions("ph:medical-record-revenue-statistics:calculate")
    @Log(title = "病历邮寄收入统计", businessType = BusinessType.UPDATE)
    @PostMapping("/calculate")
    public AjaxResult calculateRevenue(
            @RequestParam("targetDate")
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate targetDate) {

        boolean result = revenueStatisticsService.calculateAndSaveDailyRevenue(targetDate);
        return toAjax(result);
    }

    /**
     * 批量统计并保存指定日期范围的收入数据（同步处理）
     */
    @RequiresPermissions("ph:medical-record-revenue-statistics:calculate")
    @Log(title = "病历邮寄收入统计", businessType = BusinessType.UPDATE)
    @PostMapping("/calculate-range")
    public AjaxResult calculateRevenueRange(
            @RequestParam("startDate")
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate")
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        try {
            // 同步批量统计收入数据
            Map<String, Object> result = revenueStatisticsService.calculateRevenueRange(startDate, endDate);
            return success(result.get("message"));
        } catch (Exception e) {
            logger.error("批量统计收入数据时发生异常", e);
            return error("批量统计失败: " + e.getMessage());
        }
    }

    /**
     * 删除指定日期的收入统计数据
     */
    @RequiresPermissions("ph:medical-record-revenue-statistics:remove")
    @Log(title = "病历邮寄收入统计", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(
            @RequestParam("targetDate")
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate targetDate
    ) {
        boolean result = revenueStatisticsService.removeById(targetDate);
        return toAjax(result);
    }

    /**
     * 获取收入统计概览数据
     */
    @RequiresPermissions("ph:medical-record-revenue-statistics:view")
    @GetMapping("/overview")
    public AjaxResult getOverview(
            @RequestParam(value = "startDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(value = "endDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        // 处理日期参数默认值和校验
        LocalDate now = LocalDate.now();

        // 处理开始日期默认值
        if (startDate == null) {
            if (endDate != null) {
                // 只有结束日期：开始日期 = 结束日期 - 30天
                startDate = endDate.minusDays(30);
            } else {
                // 都没有指定：默认查询最近30天
                startDate = now.minusDays(30);
            }
        }

        // 处理结束日期默认值
        if (endDate == null) {
            endDate = now;
        }

        // 校验截止日期不得早于开始日期
        if (startDate.isAfter(endDate)) {
            return error("开始日期不能晚于结束日期");
        }

        // 使用数据库聚合查询获取概览数据，提高性能
        var overviewDto = revenueStatisticsService.getOverviewByDateRange(startDate, endDate);
        return success(overviewDto);
    }

}
