package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import cn.idev.excel.annotation.format.NumberFormat;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 病历邮寄业务收入统计表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "medical_record_revenue_statistics", autoResultMap = true)
@ExcelIgnoreUnannotated
public class MedicalRecordRevenueStatistics implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 交易日期（主键）
     */
    @TableId
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ExcelProperty("日期")
    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(20)
    private LocalDate transactionDate;

    /**
     * 交易笔数
     */
    @ExcelProperty("交易笔数")
    @ColumnWidth(20)
    private Integer transactionCount;

    /**
     * 交易金额
     */
    @ExcelProperty("交易金额")
    @NumberFormat("#,###0.00")
    @ColumnWidth(20)
    private BigDecimal transactionAmount;

}
