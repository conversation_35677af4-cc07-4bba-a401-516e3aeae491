package space.lzhq.ph.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 病历邮寄收入统计概览DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MedicalRecordRevenueOverviewDto {

    /**
     * 总天数（有数据的天数）
     */
    private Integer totalDays;

    /**
     * 总交易笔数
     */
    private Integer totalTransactions;

    /**
     * 总交易金额
     */
    private BigDecimal totalAmount;

    /**
     * 计算平均每日交易笔数
     *
     * @return 平均每日交易笔数
     */
    public double getAvgDailyTransactions() {
        if (totalDays == null || totalDays == 0 || totalTransactions == null) {
            return 0.0;
        }
        return totalTransactions / (double) totalDays;
    }

    /**
     * 计算平均每日交易金额
     *
     * @return 平均每日交易金额
     */
    public BigDecimal getAvgDailyAmount() {
        if (totalDays == null || totalDays == 0 || totalAmount == null) {
            return BigDecimal.ZERO;
        }
        return totalAmount.divide(BigDecimal.valueOf(totalDays), 2, RoundingMode.HALF_UP);
    }
}
