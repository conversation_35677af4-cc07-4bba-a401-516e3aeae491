package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import space.lzhq.ph.domain.MedicalRecordRevenueStatistics;
import space.lzhq.ph.dto.response.MedicalRecordRevenueOverviewDto;

import java.time.LocalDate;

/**
 * 病历邮寄业务收入统计Mapper接口
 */
@Mapper
public interface MedicalRecordRevenueStatisticsMapper extends BaseMapper<MedicalRecordRevenueStatistics> {

    /**
     * 统计指定日期的病历邮寄业务收入
     * 
     * @param targetDate 目标日期
     * @return 收入统计结果
     */
    @Select("SELECT " +
            "DATE(pay_time) as transaction_date, " +
            "COUNT(*) as transaction_count, " +
            "COALESCE(SUM(total_fee), 0) as transaction_amount " +
            "FROM medical_record_application " +
            "WHERE pay_time >= #{targetDate} AND pay_time < DATE_ADD(#{targetDate}, INTERVAL 1 DAY) " +
            "AND total_fee IS NOT NULL " +
            "GROUP BY DATE(pay_time)")
    MedicalRecordRevenueStatistics calculateDailyRevenue(@Param("targetDate") LocalDate targetDate);

    /**
     * 统计指定日期范围内的收入概览数据（从统计表查询）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收入概览统计结果
     */
    @Select("SELECT " +
            "COUNT(*) as totalDays, " +
            "COALESCE(SUM(transaction_count), 0) as totalTransactions, " +
            "COALESCE(SUM(transaction_amount), 0) as totalAmount " +
            "FROM medical_record_revenue_statistics " +
            "WHERE transaction_date >= #{startDate} AND transaction_date <= #{endDate}")
    MedicalRecordRevenueOverviewDto calculateOverviewByDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
}
