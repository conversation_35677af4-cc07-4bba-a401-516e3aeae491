package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.MedicalRecordRevenueStatistics;
import space.lzhq.ph.dto.response.MedicalRecordRevenueOverviewDto;
import space.lzhq.ph.mapper.MedicalRecordRevenueStatisticsMapper;
import space.lzhq.ph.service.IMedicalRecordRevenueStatisticsService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 病历邮寄业务收入统计Service业务层处理
 */
@Service
@Validated
public class MedicalRecordRevenueStatisticsServiceImpl extends ServiceImpl<MedicalRecordRevenueStatisticsMapper, MedicalRecordRevenueStatistics> implements IMedicalRecordRevenueStatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(MedicalRecordRevenueStatisticsServiceImpl.class);

    // 常量定义
    private static final String TOTAL_DAYS_KEY = "totalDays";
    private static final String SUCCESS_COUNT_KEY = "successCount";
    private static final String MESSAGE_KEY = "message";

    private final MedicalRecordRevenueStatisticsMapper revenueStatisticsMapper;

    @Autowired
    @Lazy
    private IMedicalRecordRevenueStatisticsService self;

    @Autowired
    public MedicalRecordRevenueStatisticsServiceImpl(MedicalRecordRevenueStatisticsMapper revenueStatisticsMapper) {
        this.revenueStatisticsMapper = revenueStatisticsMapper;
    }

    @Override
    @Transactional
    public boolean calculateAndSaveDailyRevenue(@NotNull(message = "交易日期不能为空") LocalDate transactionDate) {
        try {
            logger.info("开始统计日期 {} 的病历邮寄业务收入", transactionDate);

            // 从数据库计算当日收入统计
            MedicalRecordRevenueStatistics statistics = revenueStatisticsMapper.calculateDailyRevenue(transactionDate);

            if (statistics == null) {
                // 如果没有数据，创建一个空的统计记录
                statistics = new MedicalRecordRevenueStatistics(transactionDate, 0, BigDecimal.ZERO);
                logger.info("日期 {} 无交易数据，创建空统计记录", transactionDate);
            } else {
                logger.info("日期 {} 统计结果：交易笔数 {}，交易金额 {}",
                        transactionDate, statistics.getTransactionCount(), statistics.getTransactionAmount());
            }

            // 使用 saveOrUpdate 方法，如果记录存在则更新，不存在则插入
            boolean result = this.saveOrUpdate(statistics);

            if (result) {
                logger.info("成功保存日期 {} 的收入统计数据", transactionDate);
            } else {
                logger.error("保存日期 {} 的收入统计数据失败", transactionDate);
            }

            return result;
        } catch (Exception e) {
            logger.error("统计日期 {} 的病历邮寄业务收入时发生异常", transactionDate, e);
            return false;
        }
    }

    @Override
    public List<MedicalRecordRevenueStatistics> selectListByDateRange(LocalDate startDate, LocalDate endDate) {
        logger.info("查询收入统计数据列表，开始日期：{}，结束日期：{}", startDate, endDate);

        // 构建查询条件
        LambdaQueryWrapper<MedicalRecordRevenueStatistics> queryWrapper = buildDateRangeQueryWrapper(startDate, endDate);

        // 执行查询
        return this.list(queryWrapper);
    }

    /**
     * 构建日期范围查询条件
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 查询条件
     */
    private LambdaQueryWrapper<MedicalRecordRevenueStatistics> buildDateRangeQueryWrapper(LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<MedicalRecordRevenueStatistics> queryWrapper = Wrappers.lambdaQuery();

        // 日期范围查询
        if (startDate != null) {
            queryWrapper.ge(MedicalRecordRevenueStatistics::getTransactionDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.le(MedicalRecordRevenueStatistics::getTransactionDate, endDate);
        }

        return queryWrapper;
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Map<String, Object>> calculateRevenueRangeAsync(LocalDate startDate, LocalDate endDate) {
        logger.info("开始异步批量统计收入数据，开始日期：{}，结束日期：{}", startDate, endDate);
        
        try {
            var result = processBatchCalculation(startDate, endDate);
            logger.info("异步批量统计完成，共处理 {} 天，成功 {} 天", result.get(TOTAL_DAYS_KEY), result.get(SUCCESS_COUNT_KEY));
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            logger.error("异步批量统计收入数据时发生异常", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @Override
    public Map<String, Object> calculateRevenueRange(LocalDate startDate, LocalDate endDate) {
        logger.info("开始同步批量统计收入数据，开始日期：{}，结束日期：{}", startDate, endDate);
        var result = processBatchCalculation(startDate, endDate);
        logger.info("同步批量统计完成，共处理 {} 天，成功 {} 天", result.get(TOTAL_DAYS_KEY), result.get(SUCCESS_COUNT_KEY));
        return result;
    }

    /**
     * 处理批量计算逻辑
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处理结果
     */
    private Map<String, Object> processBatchCalculation(LocalDate startDate, LocalDate endDate) {
        int successCount = 0;
        int totalDays = 0;

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            totalDays++;
            if (self.calculateAndSaveDailyRevenue(currentDate)) {
                successCount++;
            }
            currentDate = currentDate.plusDays(1);
        }

        var result = new HashMap<String, Object>();
        result.put(TOTAL_DAYS_KEY, totalDays);
        result.put(SUCCESS_COUNT_KEY, successCount);
        result.put(MESSAGE_KEY, String.format("批量统计完成，共处理 %d 天，成功 %d 天", totalDays, successCount));

        return result;
    }

    @Override
    public MedicalRecordRevenueOverviewDto getOverviewByDateRange(LocalDate startDate, LocalDate endDate) {
        logger.info("使用数据库聚合查询收入统计概览数据，开始日期：{}，结束日期：{}", startDate, endDate);

        MedicalRecordRevenueOverviewDto overview = revenueStatisticsMapper.calculateOverviewByDateRange(startDate, endDate);

        // 处理空值情况
        if (overview == null) {
            overview = new MedicalRecordRevenueOverviewDto(0, 0, BigDecimal.ZERO);
            logger.info("指定日期范围内无数据，返回空统计结果");
        } else {
            // 处理可能的null值
            if (overview.getTotalDays() == null) overview.setTotalDays(0);
            if (overview.getTotalTransactions() == null) overview.setTotalTransactions(0);
            if (overview.getTotalAmount() == null) overview.setTotalAmount(BigDecimal.ZERO);

            logger.info("数据库聚合查询完成，统计结果：{}", overview);
        }

        return overview;
    }

}
