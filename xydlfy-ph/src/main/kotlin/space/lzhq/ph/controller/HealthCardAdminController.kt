package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.apache.shiro.authz.annotation.Logical
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.mospital.erhc.guanxin.GetPersonInfoRequest
import org.mospital.erhc.guanxin.PersonInfo
import org.mospital.erhc.guanxin.Values.CARD_TYPE_ID_CARD
import org.mospital.erhc.guanxin.Values.CARD_TYPE_PASSPORT
import org.mospital.erhc.guanxin.Values.CARD_TYPE_PR_CARD
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

@Controller
@RequestMapping("/ph/health-card")
class HealthCardAdminController : BaseController() {

    companion object {
        private const val CARD_TYPE_HEALTH_CARD = "99"
        private val CARD_TYPES: List<String> =
            listOf(CARD_TYPE_ID_CARD, CARD_TYPE_PASSPORT, CARD_TYPE_PR_CARD, CARD_TYPE_HEALTH_CARD)
    }

    @RequiresPermissions("ph:health-card:view")
    @GetMapping
    fun index(): String {
        return "ph/health-card/index"
    }

    @RequiresPermissions(value = ["ph:health-card:view", "ph:health-card:query"], logical = Logical.AND)
    @GetMapping("/query")
    @ResponseBody
    fun query(
        @RequestParam("cardType") cardType: String,
        @RequestParam("cardNo") cardNo: String
    ): AjaxResult {
        if (!CARD_TYPES.contains(cardType)) {
            return AjaxResult.error("不支持的证件类型: $cardType")
        }

        if (cardNo.isBlank()) {
            return AjaxResult.error("证件号码不能为空")
        }

        val request = if (CARD_TYPE_HEALTH_CARD == cardType) {
            GetPersonInfoRequest(erhcCardNo = cardNo)
        } else {
            GetPersonInfoRequest(idCode = cardNo, idCardTypeCode = cardType)
        }
        val ret = request.executeAsRet()
        return if (ret.isOk()) {
            val personInfo = ret.getAs<PersonInfo>("data")
            AjaxResult.success(personInfo)
        } else {
            AjaxResult.error(ret.getMsg())
        }
    }

}
