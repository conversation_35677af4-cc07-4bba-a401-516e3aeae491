package space.lzhq.ph.controller

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import jakarta.validation.constraints.NotBlank
import org.mospital.common.TokenManager
import org.mospital.yahua.PatientQueueItem
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.dto.PatientQueueItemDto
import java.net.URI

@RestController
@RequestMapping("/open")
@Validated
class OpenController {

    companion object {
        private val log: Logger = LoggerFactory.getLogger(OpenController::class.java)
    }

    /**
     * 获取医生照片
     *
     * @param queueToken 排队token，来自 `/api/menzhen/queue` 接口
     * @return 医生照片
     */
    @GetMapping("/menzhen-queue/doctor-photo")
    fun getMenzhenQueueDoctorPhoto(@RequestParam @NotBlank(message = "queueToken不能为空") queueToken: String): ResponseEntity<StreamingResponseBody> {
        val queueItem: PatientQueueItem = TokenManager.parseToken(queueToken, jacksonTypeRef())
            ?: return ResponseEntity.notFound().build()

        val originalUrl = queueItem.doctorPicture
        if (originalUrl.isNullOrBlank()) {
            return ResponseEntity.notFound().build()
        }

        val normalizedUrl = PatientQueueItemDto.normalizeUrl(originalUrl)

        try {
            val connection = URI(normalizedUrl).toURL().openConnection()
            val contentType = connection.contentType ?: "image/jpeg"
            
            val mediaType = try {
                MediaType.parseMediaType(contentType)
            } catch (e: Exception) {
                MediaType.IMAGE_JPEG
            }
            
            return ResponseEntity.ok()
                .contentType(mediaType)
                .body(StreamingResponseBody { outputStream ->
                    connection.getInputStream().use { inputStream ->
                        inputStream.copyTo(outputStream)
                    }
                })
        } catch (e: Exception) {
            log.error("下载医生照片失败: {}", originalUrl, e)
            return ResponseEntity.notFound().build()
        }
    }
}