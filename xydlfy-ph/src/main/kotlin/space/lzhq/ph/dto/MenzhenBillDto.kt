package space.lzhq.ph.dto

import org.mospital.bsoft.MenzhenBill
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime

data class MenzhenBillDto(
    /**
     * 交易时间
     */
    val transactionTime: LocalDateTime,

    /**
     * 业务类型
     */
    val businessType: String,

    /**
     * 变动金额（正数表示增加，负数表示减少）
     */
    val amount: BigDecimal,
) {

    constructor(menzhenBill: MenzhenBill) : this(
        transactionTime = menzhenBill.operationDate,
        businessType = menzhenBill.operationType,
        amount = menzhenBill.debitAmount.toBigDecimal().let { debit ->
            if (debit > BigDecimal.ZERO) debit else menzhenBill.creditAmount.toBigDecimal().negate()
        }.setScale(2, RoundingMode.HALF_UP),
    )

}