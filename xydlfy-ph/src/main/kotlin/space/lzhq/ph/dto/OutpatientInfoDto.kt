package space.lzhq.ph.dto

import org.mospital.bsoft.PatientInfo
import org.mospital.common.StringKit
import org.slf4j.LoggerFactory
import space.lzhq.ph.domain.PatientInfoToken
import java.math.BigDecimal

@ConsistentCopyVisibility
data class OutpatientInfoDto private constructor(
    val patientIdCardNo: String,
    val patientName: String,
    val patientId: String,
    val patientCardNo: String,
    val patientMobile: String,
    val balance: BigDecimal?,
    private val originalPatientInfo: PatientInfo? = null
) {

    companion object {
        private val log = LoggerFactory.getLogger(OutpatientInfoDto::class.java)

        private fun parseBalance(cardBalance: String?): BigDecimal? {
            return try {
                cardBalance?.toBigDecimal()
            } catch (e: NumberFormatException) {
                log.error("获取门诊余额失败，无效的余额值: {}", cardBalance, e)
                null
            }
        }

        /**
         * 创建普通版本的门诊信息DTO
         */
        fun create(patientInfo: PatientInfo): OutpatientInfoDto {
            return OutpatientInfoDto(
                patientIdCardNo = patientInfo.patientIdCard,
                patientName = patientInfo.patientName,
                patientId = patientInfo.patientId,
                patientCardNo = patientInfo.cardNo,
                patientMobile = patientInfo.patientPhone.orEmpty(),
                balance = parseBalance(patientInfo.cardBalance)
            )
        }

        /**
         * 创建脱敏版本的门诊信息DTO
         * patientInfoToken使用原始未脱敏的数据生成
         */
        fun createMasked(patientInfo: PatientInfo): OutpatientInfoDto {
            return OutpatientInfoDto(
                patientIdCardNo = StringKit.hideIdCardNo(patientInfo.patientIdCard),
                patientName = StringKit.hideName(patientInfo.patientName),
                patientId = patientInfo.patientId,
                patientCardNo = StringKit.hideIdCardNo(patientInfo.cardNo),
                patientMobile = patientInfo.patientPhone.orEmpty(),
                balance = parseBalance(patientInfo.cardBalance),
                originalPatientInfo = patientInfo
            )
        }

    }

    @Suppress("unused")
    val patientInfoToken: String =
        if (originalPatientInfo != null) {
            // 对于脱敏的DTO，使用原始数据生成token
            PatientInfoToken.builder()
                .patientIdCardNo(originalPatientInfo.patientIdCard)
                .patientName(originalPatientInfo.patientName)
                .patientId(originalPatientInfo.patientId)
                .patientCardNo(originalPatientInfo.cardNo)
                .patientMobile(originalPatientInfo.patientPhone.orEmpty())
                .build()
                .generateToken()
        } else {
            // 对于未脱敏的DTO，使用其自身数据
            PatientInfoToken.builder()
                .patientIdCardNo(this.patientIdCardNo)
                .patientName(this.patientName)
                .patientId(this.patientId)
                .patientCardNo(this.patientCardNo)
                .patientMobile(this.patientMobile)
                .build()
                .generateToken()
        }

}
