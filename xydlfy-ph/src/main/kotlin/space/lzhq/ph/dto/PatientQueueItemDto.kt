package space.lzhq.ph.dto

import com.fasterxml.jackson.annotation.JsonUnwrapped
import org.mospital.common.TokenManager
import org.mospital.yahua.PatientQueueItem

data class PatientQueueItemDto(
    @field:JsonUnwrapped
    val data: PatientQueueItem
) {
    val token: String = TokenManager.generateToken(data)

    companion object {

        private const val INTERNAL_URL = "http://192.168.250.114/"
        private const val EXTERNAL_URL = "http://138.139.100.4/"

        @JvmStatic
        fun normalizeUrl(url: String): String {
            return url.replace(INTERNAL_URL, EXTERNAL_URL)
        }
        
    }
}