package space.lzhq.ph.service

import com.ruoyi.system.service.ISysConfigService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.security.KeyFactory
import java.security.PublicKey
import java.security.spec.X509EncodedKeySpec
import java.util.*
import javax.crypto.Cipher

/**
 * 移动影像加密服务异常
 */
sealed class MobileImagingEncryptionException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    class ConfigurationMissing(configKey: String) :
        MobileImagingEncryptionException("移动影像RSA公钥配置为空，配置键: $configKey")

    class InvalidPublicKey(message: String, cause: Throwable? = null) :
        MobileImagingEncryptionException("RSA公钥格式无效: $message", cause)

    class EncryptionFailed(message: String, cause: Throwable? = null) :
        MobileImagingEncryptionException("RSA加密失败: $message", cause)

    class InvalidInput(message: String) : MobileImagingEncryptionException("输入参数无效: $message")
}

/**
 * 移动影像加密服务
 * 专门用于移动影像集成中的患者标识符加密
 * 
 * <AUTHOR>
 */
@Service
class MobileImagingEncryptionService(
    private val sysConfigService: ISysConfigService
) {
    
    private val logger: Logger = LoggerFactory.getLogger(MobileImagingEncryptionService::class.java)
    
    // 移动影像RSA公钥配置键名
    private companion object {
        const val MOBILE_IMAGING_PUBLIC_KEY_CONFIG = "mobile-imaging.public-key"
    }
    
    /**
     * 加密检查ID（examId）
     * 
     * @param examId 检查ID
     * @return 加密后的Base64编码字符串
     * @throws MobileImagingEncryptionException 当输入无效或配置错误时
     */
    fun encryptExamId(examId: String): String {
        if (examId.isBlank()) {
            throw MobileImagingEncryptionException.InvalidInput("检查ID不能为空或空白")
        }
        
        logger.debug("开始加密检查ID: ${examId.take(4)}****")

        val publicKeyPem = getPublicKeyConfig()
        val result = encryptWithRSA(examId, publicKeyPem)
        
        logger.info("检查ID加密成功，原始长度: ${examId.length}")
        return result
    }
    
    /**
     * 使用RSA公钥加密数据
     *
     * @throws MobileImagingEncryptionException.InvalidPublicKey 当公钥格式无效时
     * @throws MobileImagingEncryptionException.EncryptionFailed 当加密过程失败时
     */
    private fun encryptWithRSA(data: String, publicKeyPem: String): String = try {
        val publicKey = parsePublicKey(publicKeyPem)
        
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
        cipher.init(Cipher.ENCRYPT_MODE, publicKey)
        
        val encryptedBytes = cipher.doFinal(data.toByteArray(Charsets.UTF_8))
        Base64.getEncoder().encodeToString(encryptedBytes)
    } catch (e: MobileImagingEncryptionException) {
        // 让我们自己的异常类型直接传播，保持错误语义
        throw e
    } catch (e: Exception) {
        logger.warn("RSA加密失败: ${e.message}")
        throw MobileImagingEncryptionException.EncryptionFailed(e.message ?: "未知错误", e)
    }
    
    /**
     * 解析PEM格式的RSA公钥
     *
     * @throws MobileImagingEncryptionException.InvalidPublicKey 当公钥格式无效时
     */
    private fun parsePublicKey(publicKeyPem: String): PublicKey = try {
        val cleanedKey = publicKeyPem
            .replace("-----BEGIN PUBLIC KEY-----", "")
            .replace("-----END PUBLIC KEY-----", "")
            .replace("\\s".toRegex(), "")
        
        val keyBytes = Base64.getDecoder().decode(cleanedKey)
        val keySpec = X509EncodedKeySpec(keyBytes)
        
        KeyFactory.getInstance("RSA").generatePublic(keySpec)
    } catch (e: Exception) {
        logger.warn("解析RSA公钥失败: ${e.message}")
        throw MobileImagingEncryptionException.InvalidPublicKey(e.message ?: "未知错误", e)
    }
    
    /**
     * 获取公钥配置
     *
     * @throws MobileImagingEncryptionException.ConfigurationMissing 当配置为空或不存在时
     */
    private fun getPublicKeyConfig(): String {
        val publicKeyStr = sysConfigService.selectConfigByKey(MOBILE_IMAGING_PUBLIC_KEY_CONFIG)
        if (publicKeyStr.isNullOrBlank()) {
            logger.debug("移动影像RSA公钥配置为空，配置键: $MOBILE_IMAGING_PUBLIC_KEY_CONFIG")
            throw MobileImagingEncryptionException.ConfigurationMissing(MOBILE_IMAGING_PUBLIC_KEY_CONFIG)
        }
        return publicKeyStr
    }
    
    /**
     * 检查移动影像加密服务是否可用
     * 
     * @return 服务是否可用
     */
    fun isServiceAvailable(): Boolean = try {
        val publicKeyConfig = getPublicKeyConfig()
        parsePublicKey(publicKeyConfig)
        true
    } catch (e: MobileImagingEncryptionException.ConfigurationMissing) {
        logger.warn("移动影像加密服务不可用: ${e.message}")
        false
    } catch (e: MobileImagingEncryptionException.InvalidPublicKey) {
        logger.warn("移动影像加密服务不可用: ${e.message}")
        false
    }
}