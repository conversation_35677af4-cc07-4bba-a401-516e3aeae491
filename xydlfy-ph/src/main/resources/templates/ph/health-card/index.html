<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('健康卡查询')"/>
    <style>
        /* 主题颜色定义 */
        :root {
            --primary-color: #00b8a9;        /* 主色调：清新的蓝绿色 */
            --secondary-color: #4f98ca;      /* 次要色：柔和的蓝色 */
            --accent-color: #48466d;         /* 强调色：深紫色 */
            --background-color: #f7f9fc;     /* 背景色：浅灰蓝 */
            --border-color: #e3e8f0;         /* 边框色：浅灰 */
            --text-color: #2d3748;           /* 文字颜色：深灰 */
        }

        /* 整体背景 */
        .gray-bg {
            background-color: var(--background-color) !important;
        }

        /* 搜索区域样式 */
        .search-collapse {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        /* 表单控件样式 */
        .form-control {
            border-color: var(--border-color);
            border-radius: 4px;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0,184,169,0.25);
        }

        /* 查询按钮样式 */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #009e91;
            border-color: #009e91;
        }

        /* 重新查询按钮样式 */
        .btn-info {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-info:hover {
            background-color: #4180ac;
            border-color: #4180ac;
        }

        /* 面板样式 */
        .ibox {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .ibox-title {
            background-color: white;
            border-radius: 8px 8px 0 0;
            border-bottom: 2px solid var(--border-color);
        }

        .ibox-title h5 {
            color: var(--accent-color);
            font-weight: 600;
        }

        .ibox-content {
            background-color: white;
            border-radius: 0 0 8px 8px;
        }

        /* 表格样式 */
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0,184,169,0.05);
        }

        .table-bordered td {
            border-color: var(--border-color);
        }

        /* 标签样式 */
        label {
            color: var(--text-color);
            font-weight: 500;
        }

        /* 查询结果表格中的标签 */
        #result-body strong {
            color: var(--accent-color);
        }

        /* 列表项间距 */
        .select-list ul li {
            margin-right: 15px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse" style="margin-bottom: 25px;">
            <div class="select-list">
                <form id="health-card-form" class="form-inline">
                    <div class="row" style="width: 100%; margin: 0;">
                        <div class="col-sm-12">
                            <ul style="margin: 0; padding: 0;">
                                <li>
                                    <label>证件类型：</label>
                                    <select name="cardType" id="cardType" class="form-control">
                                        <!-- 选项将通过JavaScript动态生成 -->
                                    </select>
                                </li>
                                <li>
                                    <label>证件号码：</label>
                                    <input type="text" name="cardNo" id="cardNo" class="form-control"/>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="query()">
                                        <i class="fa fa-search"></i>&nbsp;查询
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 查询结果展示 -->
        <div class="col-sm-12" id="result-panel" style="display:none; padding: 0;">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>查询结果</h5>
                </div>
                <div class="ibox-content">
                    <table class="table table-bordered">
                        <tbody id="result-body">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 历史记录 -->
        <div class="col-sm-12" id="history-panel" style="display:none; padding: 0;">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>最近查询记录</h5>
                </div>
                <div class="ibox-content">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>证件类型</th>
                            <th>证件号码</th>
                            <th>姓名</th>
                            <th>查询时间</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody id="history-body">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    const MAX_HISTORY_ITEMS = 10;
    const CARD_TYPE_MAP = {
        '01': '居民身份证',
        '03': '护照',
        '10': '外国人永久居留身份证',
        '99': '健康卡'
    };

    // 动态生成证件类型选项
    function initCardTypeOptions() {
        const cardTypeSelect = $('#cardType');
        cardTypeSelect.empty(); // 清空现有选项
        
        // 添加默认选项
        cardTypeSelect.append('<option value="">请选择证件类型</option>');
        
        // 遍历CARD_TYPE_MAP生成选项
        Object.keys(CARD_TYPE_MAP).forEach(function(key) {
            cardTypeSelect.append(`<option value="${key}">${CARD_TYPE_MAP[key]}</option>`);
        });
    }

    // 查询方法
    function query() {
        const cardType = $('#cardType').val();
        const cardNo = $('#cardNo').val();

        if (!cardType) {
            $.modal.alertWarning('请选择证件类型');
            return;
        }

        if (!cardNo) {
            $.modal.alertWarning('请输入证件号码');
            return;
        }

        $.ajax({
            url: ctx + 'ph/health-card/query',
            type: 'GET',
            data: {
                cardType: cardType,
                cardNo: cardNo
            },
            success: function(res) {
                if (res.code === 0) {
                    showResult(res.data);
                    saveHistory({
                        cardType: cardType,
                        cardNo: cardNo,
                        name: res.data.decryptedName,
                        timestamp: new Date().getTime()
                    });
                } else {
                    $.modal.alertError(res.msg);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $.modal.alertError('查询失败，请稍后重试。');
                console.error('AJAX-Error:', textStatus, errorThrown);
            }
        });
    }

    // 显示查询结果
    function showResult(data) {
        const resultItems = [
            {label: '姓名', value: data.decryptedName},
            {label: '证件号码', value: data.decryptedIdCode},
            {label: '手机号', value: data.decryptedPhone},
            {label: '出生日期', value: data.birthday},
            {label: '现住址', value: data.currentAddress},
            {label: '发卡机构', value: data.cardOrgName},
            {label: '健康卡号', value: data.erhcCardNo},
        ];

        let html = '';
        resultItems.forEach(item => {
            html += `<tr><td width="150"><strong>${item.label}：</strong></td><td>${escapeHtml(String(item.value || '-'))}</td></tr>`;
        });

        $('#result-body').html(html);
        $('#result-panel').show();
    }

    // 保存查询历史
    function saveHistory(record) {
        let history = JSON.parse(localStorage.getItem('healthCardHistory') || '[]');
        history.unshift(record);
        if (history.length > MAX_HISTORY_ITEMS) {
            history = history.slice(0, MAX_HISTORY_ITEMS);
        }
        localStorage.setItem('healthCardHistory', JSON.stringify(history));
        showHistory();
    }

    // 显示历史记录
    function showHistory() {
        const history = JSON.parse(localStorage.getItem('healthCardHistory') || '[]');

        // 如果没有历史记录，隐藏整个面板
        if (!history || history.length === 0) {
            $('#history-panel').hide();
            return;
        }

        let html = '';
        history.forEach(record => {
            // 格式化时间
            const date = new Date(record.timestamp);
            const formattedTime = date.getFullYear() + '-' +
                String(date.getMonth() + 1).padStart(2, '0') + '-' +
                String(date.getDate()).padStart(2, '0') + ' ' +
                String(date.getHours()).padStart(2, '0') + ':' +
                String(date.getMinutes()).padStart(2, '0') + ':' +
                String(date.getSeconds()).padStart(2, '0');

            html += `
                <tr>
                    <td>${CARD_TYPE_MAP[record.cardType]}</td>
                    <td>${escapeHtml(record.cardNo)}</td>
                    <td>${escapeHtml(record.name)}</td>
                    <td>${formattedTime}</td>
                    <td>
                        <a class="btn btn-xs btn-info"
                           data-card-type="${escapeHtml(record.cardType)}"
                           data-card-no="${escapeHtml(record.cardNo)}"
                           onclick="reQuerySafe(this)">
                            重新查询
                        </a>
                    </td>
                </tr>
            `;
        });

        $('#history-body').html(html);
        $('#history-panel').show();
    }

    // 添加 HTML 转义函数
    function escapeHtml(unsafe) {
        // 处理null、undefined或非字符串输入
        if (unsafe == null) {
            return '';
        }
        
        // 确保输入是字符串
        const str = String(unsafe);
        return str
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 修改重新查询函数为安全版本
    function reQuerySafe(element) {
        const cardType = $(element).data('cardType');
        const cardNo = $(element).data('cardNo');
        $('#cardType').val(cardType);
        $('#cardNo').val(cardNo);
        query();
    }

    // 页面加载完成后初始化页面
    $(function() {
        initCardTypeOptions(); // 初始化证件类型选项
        showHistory(); // 显示历史记录
    });
</script>
</body>
</html>