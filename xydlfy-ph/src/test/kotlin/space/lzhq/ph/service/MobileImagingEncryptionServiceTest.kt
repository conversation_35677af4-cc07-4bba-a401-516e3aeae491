package space.lzhq.ph.service

import com.ruoyi.system.service.ISysConfigService
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.util.*
import javax.crypto.Cipher

class MobileImagingEncryptionServiceTest {

    @Mock
    private lateinit var sysConfigService: ISysConfigService

    private lateinit var encryptionService: MobileImagingEncryptionService
    private lateinit var testKeyPair: KeyPair
    private lateinit var testPublicKeyString: String

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)

        // 通过构造函数直接注入mock，无需反射
        encryptionService = MobileImagingEncryptionService(sysConfigService)

        // 生成测试用的RSA密钥对
        val keyGenerator = KeyPairGenerator.getInstance("RSA")
        keyGenerator.initialize(2048)
        testKeyPair = keyGenerator.generateKeyPair()

        // 将公钥转换为PEM格式字符串
        val publicKeyBytes = testKeyPair.public.encoded
        val base64PublicKey = Base64.getEncoder().encodeToString(publicKeyBytes)
        testPublicKeyString = "-----BEGIN PUBLIC KEY-----\n$base64PublicKey\n-----END PUBLIC KEY-----"
    }

    @Test
    fun `encryptExamId should return encrypted string when valid exam ID provided`() {
        // Given
        val examId = "EXAM-2024-001"
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.public-key"))
            .thenReturn(testPublicKeyString)

        // When
        val result = encryptionService.encryptExamId(examId)

        // Then
        assertTrue(result.isNotBlank(), "检查ID加密结果不应为空")

        // 验证可以用私钥解密
        val decryptedData = decryptWithPrivateKey(result)
        assertEquals(examId, decryptedData, "解密后的检查ID应与原始数据一致")
    }

    @Test
    fun `encryptExamId should throw exception when exam ID is blank`() {
        // When & Then
        val exception = assertThrows<MobileImagingEncryptionException.InvalidInput> {
            encryptionService.encryptExamId("   ")
        }
        assertTrue(exception.message!!.contains("检查ID不能为空或空白"), "异常消息应包含正确的错误信息")
    }

    @Test
    fun `encryptExamId should throw exception when public key config is null`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.public-key"))
            .thenReturn(null)

        // When & Then
        val exception = assertThrows<MobileImagingEncryptionException.ConfigurationMissing> {
            encryptionService.encryptExamId("EXAM-123")
        }
        assertTrue(exception.message!!.contains("mobile-imaging.public-key"), "异常消息应包含配置键名")
    }

    @Test
    fun `encryptExamId should throw exception when public key format is invalid`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.public-key"))
            .thenReturn("invalid-key-format")

        // When & Then
        val exception = assertThrows<MobileImagingEncryptionException.InvalidPublicKey> {
            encryptionService.encryptExamId("EXAM-123")
        }
        assertTrue(exception.message!!.contains("RSA公钥格式无效"), "异常消息应包含格式错误信息")
    }

    @Test
    fun `isServiceAvailable should return true when valid public key configured`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.public-key"))
            .thenReturn(testPublicKeyString)

        // When
        val result = encryptionService.isServiceAvailable()

        // Then
        assertTrue(result, "有效公钥配置时服务应可用")
    }

    @Test
    fun `isServiceAvailable should return false when public key config is invalid`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.public-key"))
            .thenReturn("invalid-key")

        // When
        val result = encryptionService.isServiceAvailable()

        // Then
        assertFalse(result, "无效公钥配置时服务应不可用")
    }

    @Test
    fun `isServiceAvailable should return false when public key config is null`() {
        // Given
        `when`(sysConfigService.selectConfigByKey("mobile-imaging.public-key"))
            .thenReturn(null)

        // When
        val result = encryptionService.isServiceAvailable()

        // Then
        assertFalse(result, "公钥配置为null时服务应不可用")
    }

    /**
     * 使用私钥解密数据（用于测试验证）
     */
    private fun decryptWithPrivateKey(encryptedData: String): String {
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
        cipher.init(Cipher.DECRYPT_MODE, testKeyPair.private)

        val encryptedBytes = Base64.getDecoder().decode(encryptedData)
        val decryptedBytes = cipher.doFinal(encryptedBytes)

        return String(decryptedBytes, Charsets.UTF_8)
    }
}