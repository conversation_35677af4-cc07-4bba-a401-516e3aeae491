#!/bin/bash

# Zyxcx 服务安装和管理脚本
# 适用于 Debian 12 系统

set -e

readonly SERVICE_NAME="zyxcx"
readonly SERVICE_FILE="zyxcx.service"
readonly SYSTEMD_DIR="/etc/systemd/system"
readonly APP_USER="ecs-user"
readonly APP_DIR="/home/<USER>/zyxcx"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0 $*"
        exit 1
    fi
}

# 检查系统
check_system() {
    if ! command -v systemctl &> /dev/null; then
        log_error "系统不支持systemd"
        exit 1
    fi
    
    if [[ ! -f /etc/debian_version ]]; then
        log_warning "检测到非Debian系统，可能存在兼容性问题"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java运行环境未安装"
        log_info "请先安装Java: sudo apt update && sudo apt install openjdk-17-jre-headless"
        exit 1
    fi
    
    # 检查用户是否存在
    if ! id "$APP_USER" &>/dev/null; then
        log_error "用户 $APP_USER 不存在"
        log_info "请先创建用户: sudo useradd -m -s /bin/bash $APP_USER"
        exit 1
    fi
    
    # 检查应用目录
    if [[ ! -d "$APP_DIR" ]]; then
        log_error "应用目录 $APP_DIR 不存在"
        exit 1
    fi
    
    # 检查JAR文件
    if [[ ! -f "$APP_DIR/zyxcx.jar" ]]; then
        log_error "JAR文件 $APP_DIR/zyxcx.jar 不存在"
        exit 1
    fi
    
    # 检查配置文件
    if [[ ! -f "$APP_DIR/application-prod.yml" ]]; then
        log_error "配置文件 $APP_DIR/application-prod.yml 不存在"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    # 创建日志目录
    mkdir -p "$APP_DIR/logs"
    chown -R $APP_USER:$APP_USER "$APP_DIR/logs"
    chmod 755 "$APP_DIR/logs"
    
    log_success "目录创建完成"
}

# 安装服务
install_service() {
    log_info "安装 $SERVICE_NAME 服务..."
    
    # 检查服务文件是否存在
    if [[ ! -f "$SERVICE_FILE" ]]; then
        log_error "服务文件 $SERVICE_FILE 不存在"
        exit 1
    fi
    
    # 停止现有服务（如果存在）
    if systemctl is-active --quiet $SERVICE_NAME; then
        log_info "停止现有服务..."
        systemctl stop $SERVICE_NAME
    fi
    
    # 复制服务文件
    cp "$SERVICE_FILE" "$SYSTEMD_DIR/"
    chmod 644 "$SYSTEMD_DIR/$SERVICE_FILE"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable $SERVICE_NAME
    
    log_success "$SERVICE_NAME 服务安装完成"
}

# 启动服务
start_service() {
    log_info "启动 $SERVICE_NAME 服务..."
    
    systemctl start $SERVICE_NAME
    
    for i in {1..10}; do
        if systemctl is-active --quiet "$SERVICE_NAME"; then
            log_success "$SERVICE_NAME 服务启动成功"
            show_status
            return 0
        fi
        log_info "等待服务启动... ($i/10)"
        sleep 3
    done
    log_error "$SERVICE_NAME 服务启动失败"
    log_info "查看日志: sudo journalctl -u $SERVICE_NAME -f"
    exit 1
}

# 停止服务
stop_service() {
    log_info "停止 $SERVICE_NAME 服务..."
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        systemctl stop $SERVICE_NAME
        log_success "$SERVICE_NAME 服务已停止"
    else
        log_warning "$SERVICE_NAME 服务未运行"
    fi
}

# 重启服务
restart_service() {
    log_info "重启 $SERVICE_NAME 服务..."
    
    systemctl restart $SERVICE_NAME
    
    # 等待服务启动
    sleep 3
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        log_success "$SERVICE_NAME 服务重启成功"
        show_status
    else
        log_error "$SERVICE_NAME 服务重启失败"
        log_info "查看日志: sudo journalctl -u $SERVICE_NAME -f"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    echo
    log_info "服务状态:"
    systemctl status $SERVICE_NAME --no-pager -l
    
    echo
    log_info "服务是否开机自启: $(systemctl is-enabled $SERVICE_NAME)"
    log_info "服务是否运行: $(systemctl is-active $SERVICE_NAME)"
}

# 查看日志
show_logs() {
    local lines=${1:-50}
    log_info "显示最近 $lines 行日志:"
    journalctl -u $SERVICE_NAME -n $lines --no-pager
}

# 实时查看日志
follow_logs() {
    log_info "实时查看日志 (Ctrl+C 退出):"
    journalctl -u $SERVICE_NAME -f
}

# 卸载服务
uninstall_service() {
    log_info "卸载 $SERVICE_NAME 服务..."
    
    # 停止服务
    if systemctl is-active --quiet $SERVICE_NAME; then
        systemctl stop $SERVICE_NAME
    fi
    
    # 禁用服务
    if systemctl is-enabled --quiet $SERVICE_NAME; then
        systemctl disable $SERVICE_NAME
    fi
    
    # 删除服务文件
    if [[ -f "$SYSTEMD_DIR/$SERVICE_FILE" ]]; then
        rm -f "$SYSTEMD_DIR/$SERVICE_FILE"
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    systemctl reset-failed
    
    log_success "$SERVICE_NAME 服务卸载完成"
}

# 显示帮助信息
show_help() {
    echo "Zyxcx 服务管理脚本"
    echo
    echo "用法: $0 [命令]"
    echo
    echo "命令:"
    echo "  install     安装并启动服务"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      显示服务状态"
    echo "  logs [n]    显示最近n行日志 (默认50行)"
    echo "  follow      实时查看日志"
    echo "  uninstall   卸载服务"
    echo "  help        显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 install           # 安装并启动服务"
    echo "  $0 status            # 查看服务状态"
    echo "  $0 logs 100          # 查看最近100行日志"
    echo "  $0 follow            # 实时查看日志"
}

# 主函数
main() {
    case "${1:-help}" in
        install)
            check_root
            check_system
            check_dependencies
            create_directories
            install_service
            start_service
            ;;
        start)
            check_root
            start_service
            ;;
        stop)
            check_root
            stop_service
            ;;
        restart)
            check_root
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        follow)
            follow_logs
            ;;
        uninstall)
            check_root
            uninstall_service
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
