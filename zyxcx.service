[Unit]
Description=Zyxcx Application Service
Documentation=https://github.com/your-org/zyxcx
After=network-online.target
Wants=network-online.target
ConditionPathExists=/home/<USER>/zyxcx/zyxcx.jar

[Service]
Type=simple
User=ecs-user
Group=ecs-user
WorkingDirectory=/home/<USER>/zyxcx

# 环境变量配置
Environment="LOG_DIR=/home/<USER>/zyxcx/logs"
Environment="JAR_PATH=/home/<USER>/zyxcx/zyxcx.jar"
Environment="PROFILE_FILE=/home/<USER>/zyxcx/application-prod.yml"

# Java应用启动命令
ExecStart=/usr/bin/java \
  -Xms2g -Xmx3g \
  -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=100 \
  -XX:G1HeapRegionSize=4m \
  -XX:InitiatingHeapOccupancyPercent=35 \
  -XX:SoftRefLRUPolicyMSPerMB=0 \
  -XX:+AlwaysPreTouch \
  --add-opens java.base/java.lang=ALL-UNNAMED \
  --add-opens java.base/java.io=ALL-UNNAMED \
  --add-opens java.base/java.util=ALL-UNNAMED \
  --add-opens java.base/java.util.concurrent=ALL-UNNAMED \
  -Xlog:gc*=info:file=${LOG_DIR}/gc-%%t.log:time,uptime,level,tags:filecount=10,filesize=10M \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=${LOG_DIR}/heapdump-%%p.hprof \
  -Djava.security.egd=file:/dev/urandom \
  -Dspring.config.additional-location=${PROFILE_FILE} \
  -Dspring.profiles.active=prod \
  -jar ${JAR_PATH}

# 优雅停止命令
ExecStop=/bin/kill -TERM $MAINPID

# 重启策略配置
Restart=always
RestartSec=10
StartLimitBurst=3
StartLimitInterval=60

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

# 输出配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=zyxcx

# 超时设置
TimeoutStartSec=300
TimeoutStopSec=30
KillMode=mixed
KillSignal=SIGTERM

# 安全配置
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/home/<USER>/zyxcx
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target

